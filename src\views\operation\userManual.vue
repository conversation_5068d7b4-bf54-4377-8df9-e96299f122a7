<template>
  <el-card header="新手手册">
    <WangEditor v-model="state.content" :toolbarConfig="state.toolbarConfig"></WangEditor>
    <el-button class="mt20" type="primary" @click="submitForm">保存</el-button>
  </el-card>
</template>
<script setup name="userManual" pageName="新手手册">
import { reactive, ref } from 'vue';
import WangEditor from '@/components/WangEditor/index.vue';
import { superOptNewbieQueryApi, superOptNewbieSaveApi } from '@/api/modules/operation.js';
const state = reactive({
  content: '',
  toolbarConfig: {
    // excludeKeys: ['group-video']
  }
});
const submitForm = async () => {
  let res = await superOptNewbieSaveApi({
    content: state.content
  });
  if (res && !res.hasErrors) {
    ElMessage.success('提交成功');
  }
};
const superOptNewbieQuery = async () => {
  let res = await superOptNewbieQueryApi();
  if (res && !res.hasErrors) {
    state.content = res.data;
  }
};
superOptNewbieQuery();
</script>
<style scoped lang="scss"></style>

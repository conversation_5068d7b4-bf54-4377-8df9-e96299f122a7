import http from '@/api';

// 订单中心-异常订单列表
export const superOrderAbnormalListApi = (data) => {
  return http.post('/super/admin/order/abnormal/list', data);
};

// 订单中心-自营护工列表
export const superOrderSelfCarerListApi = (data) => {
  return http.post('/super/admin/order/self/carer/list', data);
};

// 订单中心-确认更换护工
export const superOrderChangeCarerApi = (data) => {
  return http.post('/super/admin/order/change/carer', data);
};

// 订单中心-异常订单-确认处理
export const superOrderAbnormalConfirmApi = (data) => {
  return http.post('/super/admin/order/abnormal/confirm', data);
};

// 订单中心-订单列表
export const superOrderListApi = (data) => {
  return http.post('/super/admin/order/list', data);
};

// 订单中心-订单详情
export const superOrderQueryApi = (data) => {
  return http.post('/super/admin/order/query', data);
};

// 订单中心-订单详情-陪护记录
export const superOrderCarerRecordApi = (data) => {
  return http.post('/super/admin/order/carer/record', data);
};

// 订单中心-订单详情-服务评价
export const superOrderCommentListApi = (data) => {
  return http.post('/super/admin/order/comment/list', data);
};

// 订单中心-订单详情-操作日志
export const superOrderOprListApi = (data) => {
  return http.post('/super/admin/order/opr/list', data);
};

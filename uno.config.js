/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/05 21:30:57
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { defineConfig, presetAttributify, presetIcons, presetWind3, transformerCompileClass } from 'unocss';
import presetRemToPx from '@unocss/preset-rem-to-px';
import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders';
import { getIcons } from './build/index.js';

const icons = getIcons();
const collections = Object.fromEntries(
  Object.keys(icons).map((item) => [item, FileSystemIconLoader(`src/assets/icons/${item}`)])
);
console.log('🧩 icons collections loaded:', Object.keys(collections));
// 正确的 safelist
const safelist = Object.entries(icons).flatMap(([collection, names]) => names.map((name) => `i-${collection}:${name}`));
export default defineConfig({
  presets: [
    presetWind3(),
    presetAttributify(),
    transformerCompileClass(),
    presetIcons({
      warn: true,
      prefix: ['i-'],
      extraProperties: {
        display: 'inline-block',
        width: '1em',
        height: '1em'
      },
      collections
    }),
    // 为什么要用到这个插件？
    // 使用 viewport 作为移动端适配方案，unocss 默认单位为 rem
    // 所以需要转成 px，然后由 postcss 把 px 转成 vw/vh，完成适配
    // 这里为什么要设置基础字体大小？看下面这篇文章
    // https://juejin.cn/post/7262975395620618298
    presetRemToPx({ baseFontSize: 4 })
  ],
  safelist: Object.values(icons),
  shortcuts: {
    fc: 'flex justify-center',
    fyb: 'flex justify-between items-center',
    fyc: 'flex items-center',
    fxc: 'flex flex-col items-center',
    fcc: 'flex justify-center items-center',
    pe: 'pointer-events-none',
    't-hide': 'truncate',
    tac: 'text-center',
    centered: 'absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2',
    'centered-x': 'absolute left-1/2 transform -translate-x-1/2',
    'centered-y': 'absolute top-1/2 transform -translate-y-1/2'
  },
  rules: [
    // 圆角
    [
      /^bdr-?(\d*)$/,
      ([, d]) => ({
        'border-radius': `${d}px`
      })
    ]
  ],
  theme: {
    colors: {
      primary: 'var(--el-color-primary)',
      dark: '#18181c',
      light_border: '#efeff5',
      dark_border: '#2d2d30'
    }
  }
});

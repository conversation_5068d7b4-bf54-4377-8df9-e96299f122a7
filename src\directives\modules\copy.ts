import type { Directive } from 'vue';
import { ElMessage } from 'element-plus';
import { useClipboard, usePermission } from '@vueuse/core';

interface CopyHTMLElement extends HTMLElement {
  _copyText: string;
}

function install() {
  const { isSupported, copy } = useClipboard({
    legacy: true
  });

  function copyHandler(this: any) {
    copy(this._copyText);
    ElMessage.success('复制成功');
  }

  function updataClipboard(el: CopyHTMLElement, text: string) {
    el._copyText = text;
    el.addEventListener('click', copyHandler);
  }
  const copyDirective: Directive<CopyHTMLElement, string> = {
    mounted(el, binding) {
      updataClipboard(el, binding.value);
    },
    updated(el, binding) {
      el._copyText = binding.value;
      // updataClipboard(el, binding.value);
    },
    unmounted(el) {
      el.removeEventListener('click', copyHandler);
    }
  };
  return copyDirective;
}
const copyDirective = install();
export default copyDirective;

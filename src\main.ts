import { createApp } from 'vue';
import App from './App.vue';
import 'virtual:uno.css';

// reset style sheet
import '@/styles/reset.scss';
// CSS common style sheet
import '@/styles/common.scss';
// iconfont css
import '@/assets/iconfont/iconfont.scss';
// font css
// import '@/assets/fonts/font.scss';
// element css
import 'element-plus/dist/index.css';
// element dark css
import 'element-plus/theme-chalk/dark/css-vars.css';
// custom element dark css
import '@/styles/element-dark.scss';
// custom element css
import '@/styles/element.scss';
// svg icons
import 'virtual:svg-icons-register';
// element plus
import ElementPlus from 'element-plus';
// element icons
import * as Icons from '@element-plus/icons-vue';
// custom directives
import directives from '@/directives/index';
// vue Router
import router from '@/routers';
// pinia store
import pinia from '@/stores';
// errorHandler
import errorHandler from '@/utils/errorHandler';
import ProTable from '@/components/ProTable/index.vue';
import DatePicker from '@/components/DatePicker/index.vue';
import { formatDateTime } from '@/utils/formatter';
const app = createApp(App);

app.config.globalProperties.$formatDateTime = formatDateTime;
app.config.errorHandler = errorHandler;

// register the element Icons component
Object.keys(Icons).forEach((key) => {
  app.component(key, Icons[key as keyof typeof Icons]);
});
app.component(ProTable.name as string, ProTable);
app.component(DatePicker.name as string, DatePicker);

app.use(ElementPlus).use(directives).use(router).use(pinia).mount('#app');

<template>
  <div class="upload-box">
    <el-upload
      ref="uploadRef"
      :id="uuid"
      action="#"
      list-type="picture-card"
      :class="['upload', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']"
      :file-list="showFileList"
      :multiple="enableCrop ? false : true"
      :disabled="self_disabled"
      :limit="limit"
      :http-request="handleHttpUpload"
      :before-upload="beforeUpload"
      :on-exceed="handleExceed"
      :on-success="uploadSuccess"
      :on-error="uploadError"
      :drag="drag"
      :accept="fileType.join(',')">
      <div class="upload-empty">
        <slot name="empty">
          <el-icon><UploadFilled /></el-icon>
          <span>
            <span class="text-primary">点击上传</span>
            /拖拽到此区域
          </span>
        </slot>
      </div>
      <template #file="{ file, index }">
        <img :src="file.url" class="upload-image" />
        <div class="upload-handle" @click.stop>
          <div v-if="!self_disabled" class="handle-icon" @click="editImg(file, index)">
            <el-icon><Edit /></el-icon>
            <span>编辑</span>
          </div>
          <div class="handle-icon" @click="handlePictureCardPreview(file)">
            <el-icon><ZoomIn /></el-icon>
            <span>查看</span>
          </div>
          <div class="handle-icon" @click="handleRemove(file)" v-if="!self_disabled">
            <el-icon><Delete /></el-icon>
            <span>删除</span>
          </div>
        </div>
      </template>
    </el-upload>
    <div class="el-upload__tip">
      <slot name="tip">照片大小不能超过 5M</slot>
    </div>
    <el-image-viewer v-if="imgViewVisible" @close="imgViewVisible = false" :url-list="[viewImageUrl]" />

    <!-- 图片裁剪弹窗 -->
    <el-dialog
      v-model="cropDialogVisible"
      title="图片裁剪"
      width="800px"
      @close="cancelCrop"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="crop-container">
        <vue-cropper
          ref="cropperRef"
          :img="cropImageUrl"
          :output-size="1"
          :output-type="cropOutputType"
          :info="true"
          :full="true"
          :can-move="false"
          :can-move-box="isCropEnabled"
          :original="false"
          :auto-crop="isCropEnabled"
          :auto-crop-width="cropImageWidth"
          :auto-crop-height="cropImageHeight"
          :fixed-box="false"
          :can-scale="true"
          :center-box="false"
          :high="true"
          :info-true="true"
          :max-img-size="3000"
          :enlarge="1"
          :mode="'contain'"
          @real-time="realTime"></vue-cropper>
      </div>
      <template #footer>
        <div class="crop-controls">
          <div class="crop-switch">
            <el-switch v-model="isCropEnabled" active-text="启用裁剪" inactive-text="仅旋转" @change="onCropToggle"></el-switch>
          </div>
        </div>
        <div class="crop-actions">
          <el-button @click="rotateLeft">
            <el-icon><RefreshLeft /></el-icon>
            左旋转
          </el-button>
          <el-button @click="rotateRight">
            <el-icon><RefreshRight /></el-icon>
            右旋转
          </el-button>
          <el-button v-if="isCropEnabled" @click="resetCrop">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
        <div class="dialog-footer">
          <el-button @click="cancelCrop">取消</el-button>
          <el-button type="primary" @click="confirmCrop">
            {{ isCropEnabled ? '确认裁剪' : '确认' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="UploadImgs">
import { ref, computed, inject } from 'vue';
import { ZoomIn, Delete, Plus, Edit, RefreshLeft, RefreshRight, Switch, Sort, Refresh } from '@element-plus/icons-vue';
import { generateUUID } from '@/utils';
import { cloneDeep } from 'lodash-es';
import { VueCropper } from 'vue-cropper';
import 'vue-cropper/dist/index.css';

import { uploadImg } from '@/api/modules/upload';
import type { UploadProps, UploadFile, UploadFiles, UploadRequestOptions } from 'element-plus';
import { ElNotification, formContextKey, formItemContextKey } from 'element-plus';

interface UploadFileProps {
  api?: (params: any) => Promise<any>; // 上传图片的 api 方法
  drag?: boolean; // 是否支持拖拽上传
  disabled?: boolean; // 是否禁用上传组件
  limit?: number; // 最大图片上传数
  fileSize?: number; // 图片大小限制
  fileType?: File.ImageMimeType[]; // 图片类型限制
  height?: string; // 组件高度
  width?: string; // 组件宽度
  borderRadius?: string; // 组件边框圆角
  enableCrop?: boolean; // 是否启用裁剪功能
}

const modelValue = defineModel({
  type: Array,
  default: () => []
});

const props = withDefaults(defineProps<UploadFileProps>(), {
  drag: true,
  disabled: false,
  limit: 5,
  fileSize: 5,
  fileType: () => ['image/jpeg', 'image/png', 'image/gif'],
  height: '150px',
  width: '150px',
  borderRadius: '8px',
  enableCrop: false
});

// 生成组件唯一id
const uuid = ref('id-' + generateUUID());
// 获取 el-form 组件上下文
const formContext = inject(formContextKey, void 0);
// 获取 el-form-item 组件上下文
const formItemContext = inject(formItemContextKey, void 0);
// 判断是否禁用上传和删除
const self_disabled = computed(() => {
  return props.disabled || formContext?.disabled;
});

const showFileList: any = computed(() => {
  return modelValue.value.map((d, i) => {
    return { url: d, uid: Date.now() + i };
  });
});

const imgUid = ref(-1);

// Upload 组件引用
const uploadRef = ref();

// 裁剪相关状态
const cropDialogVisible = ref(false);
const cropImageUrl = ref('');
const cropOutputType = ref('jpeg');
const cropperRef = ref();
const pendingFile = ref<File | null>(null);
const pendingOptions = ref<UploadRequestOptions | null>(null);
const cropImageWidth = ref(300);
const cropImageHeight = ref(300);
const isCropEnabled = ref(false); // 控制是否启用裁剪功能

/**
 * @description 从URL加载图片并转换为File对象
 */
const loadImageAsFile = (imageUrl: string): Promise<File> => {
  return new Promise((resolve, reject) => {
    console.log('开始加载图片:', imageUrl);

    // 方法1: 尝试直接fetch（适用于同域或支持CORS的图片）
    const tryDirectFetch = () => {
      console.log('尝试方法1: 直接fetch');
      return fetch(imageUrl, {
        method: 'GET',
        mode: 'cors',
        credentials: 'omit'
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.blob();
        })
        .then((blob) => {
          const urlParts = imageUrl.split('/');
          const fileName = urlParts[urlParts.length - 1] || `image_${Date.now()}.jpg`;
          return new File([blob], fileName, {
            type: blob.type || 'image/jpeg',
            lastModified: Date.now()
          });
        });
    };

    // 方法2: 使用Canvas方法（适用于CORS受限的情况）
    const tryCanvasMethod = () => {
      console.log('尝试方法2: Canvas方法');
      return new Promise<File>((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';

        img.onload = () => {
          console.log('图片加载成功，尺寸:', img.width, 'x', img.height);
          try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (!ctx) {
              throw new Error('无法获取Canvas上下文');
            }

            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);

            canvas.toBlob(
              (blob) => {
                if (!blob) {
                  reject(new Error('Canvas转换失败'));
                  return;
                }
                console.log('Canvas转换成功，Blob大小:', blob.size, 'type:', blob.type);
                const urlParts = imageUrl.split('/');
                const fileName = urlParts[urlParts.length - 1] || `image_${Date.now()}.jpg`;
                const file = new File([blob], fileName, {
                  type: blob.type || 'image/jpeg',
                  lastModified: Date.now()
                });
                resolve(file);
              },
              'image/jpeg',
              0.9
            );
          } catch (error) {
            reject(error);
          }
        };

        img.onerror = (error) => {
          console.error('图片加载失败:', error);
          reject(new Error('图片加载失败'));
        };

        console.log('开始加载图片到Image元素');
        img.src = imageUrl;
      });
    };

    // 按优先级尝试不同的方法
    tryDirectFetch()
      .then((file) => {
        console.log('图片加载成功');
        resolve(file);
      })
      .catch((error) => {
        console.log('主要方法失败，尝试备用方案，错误:', error.message);
        tryCanvasMethod()
          .then((file) => {
            console.log('Canvas方法成功');
            resolve(file);
          })
          .catch((canvasError) => {
            console.error('Canvas方法失败:', {
              original: error.message,
              canvas: canvasError.message
            });
            reject(new Error(`无法加载图片，所有方法都失败`));
          });
      });
  });
};

/**
 * @description 处理裁剪后的文件上传
 */
const handleCroppedUpload = async (formData: FormData) => {
  try {
    console.log('imgUid.value: ', imgUid.value);
    const api = props.api ?? uploadImg;
    const { data } = await api(formData);

    let newImgList = cloneDeep(modelValue.value);
    if (imgUid.value !== -1) {
      newImgList.splice(imgUid.value, 1, data);
      imgUid.value = -1;
    } else {
      newImgList.push(data);
    }
    modelValue.value = newImgList;

    // 调用 el-form 内部的校验方法
    formItemContext?.prop && formContext?.validateField([formItemContext.prop as string]);

    ElNotification({
      title: '温馨提示',
      message: '图片上传成功！',
      type: 'success'
    });
    // 关闭弹窗并清理状态
    resetCropDialog();
  } catch (error) {
    ElNotification({
      title: '温馨提示',
      message: '图片上传失败，请您重新上传！',
      type: 'error'
    });
  }
};

/**
 * @description 编辑图片
 */
const editImg = (file: any, index: number) => {
  imgUid.value = index;

  console.log('编辑图片信息:', {
    fileUrl: file.url,
    fileUid: file.uid
  });

  // 使用多种方法尝试获取图片数据，避免CORS问题
  loadImageAsFile(file.url)
    .then((imageFile) => {
      // 调用裁剪弹窗
      openCropDialog(imageFile);
    })
    .catch((error) => {
      console.error('获取图片失败:', error);
      ElNotification({
        title: '温馨提示',
        message: '无法加载图片进行编辑，请重新上传图片',
        type: 'warning'
      });
      // 如果获取图片失败，回退到原来的替换方式
      const dom = document.querySelector(`#${uuid.value} .el-upload__input`);
      dom && dom.dispatchEvent(new MouseEvent('click'));
    });
};

/**
 * @description 文件上传之前判断
 */
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const imgSize = rawFile.size / 1024 / 1024 < props.fileSize;
  const imgType = props.fileType.includes(rawFile.type as File.ImageMimeType);

  if (!imgType) {
    ElNotification({
      title: '温馨提示',
      message: '上传图片不符合所需的格式！',
      type: 'warning'
    });
    return false;
  }

  if (!imgSize) {
    ElNotification({
      title: '温馨提示',
      message: `上传图片大小不能超过 ${props.fileSize}M！`,
      type: 'warning'
    });
    return false;
  }

  // 如果启用裁剪功能，拦截上传流程
  if (props.enableCrop) {
    openCropDialog(rawFile);
    return false; // 阻止默认上传
  }

  return true;
};

/**
 * @description 打开裁剪弹窗
 */
const openCropDialog = (file: File) => {
  pendingFile.value = file;
  cropOutputType.value = file.type.split('/')[1] || 'jpeg';

  const reader = new FileReader();
  reader.onload = (e) => {
    cropImageUrl.value = e.target?.result as string;

    // 创建图片对象来获取原始尺寸
    const img = new Image();
    img.onload = () => {
      // 设置裁剪框默认为图片的完整尺寸
      cropImageWidth.value = img.naturalWidth;
      cropImageHeight.value = img.naturalHeight;
      cropDialogVisible.value = true;

      // 根据当前状态决定是否显示裁剪框
      setTimeout(() => {
        if (isCropEnabled.value) {
          cropperRef.value?.startCrop();
        } else {
          cropperRef.value?.stopCrop();
          cropperRef.value?.clearCrop();
        }
      }, 100); // 延迟确保组件已渲染
    };
    img.src = e.target?.result as string;
  };
  reader.readAsDataURL(file);
};

/**
 * @description 裁剪操作方法
 */
const rotateLeft = () => {
  cropperRef.value?.rotateLeft();
};

const rotateRight = () => {
  cropperRef.value?.rotateRight();
};

const resetCrop = () => {
  cropperRef.value?.refresh();
  // 重置后重新设置为完整图片尺寸
  if (cropImageUrl.value) {
    const img = new Image();
    img.onload = () => {
      cropImageWidth.value = img.naturalWidth;
      cropImageHeight.value = img.naturalHeight;
      // 重置后根据当前状态控制裁剪框显示
      setTimeout(() => {
        if (isCropEnabled.value) {
          cropperRef.value?.startCrop();
        } else {
          cropperRef.value?.stopCrop();
          cropperRef.value?.clearCrop();
        }
      }, 100);
    };
    img.src = cropImageUrl.value;
  }
};

const realTime = (data: any) => {
  // 实时预览回调，可以在这里处理实时数据
};

/**
 * @description 切换裁剪功能
 */
const onCropToggle = (enabled: string | number | boolean) => {
  const isEnabled = Boolean(enabled);

  if (isEnabled && cropImageUrl.value) {
    // 启用裁剪时，设置裁剪框为图片完整尺寸
    const img = new Image();
    img.onload = () => {
      cropImageWidth.value = img.naturalWidth;
      cropImageHeight.value = img.naturalHeight;
      // 强制显示裁剪框
      cropperRef.value?.startCrop();
    };
    img.src = cropImageUrl.value;
  } else {
    // 禁用裁剪时，隐藏裁剪框
    cropperRef.value?.stopCrop();
    cropperRef.value?.clearCrop();
  }
};

/**
 * @description 确认裁剪
 */
const confirmCrop = () => {
  cropperRef.value?.getCropBlob((blob: Blob) => {
    if (!blob || !pendingFile.value) return;

    // 创建新的 File 对象
    const croppedFile = new File([blob], pendingFile.value.name, {
      type: blob.type,
      lastModified: Date.now()
    });

    // 继续上传流程
    const formData = new FormData();
    formData.append('file', croppedFile);
    handleCroppedUpload(formData);

    /* handleHttpUpload({
      // @ts-expect-error file不匹配，但功能正常
      file: croppedFile,
      // @ts-expect-error uploadSuccess 函数签名与 onSuccess 回调不匹配，但功能正常
      onSuccess: uploadSuccess
    });
 */
  });
};

/**
 * @description 取消裁剪
 */
const cancelCrop = () => {
  imgUid.value = -1;
  resetCropDialog();
};

/**
 * @description 重置裁剪弹窗状态
 */
const resetCropDialog = () => {
  cropDialogVisible.value = false;
  pendingFile.value = null;
  cropImageUrl.value = '';
  // 重置裁剪尺寸为默认值
  cropImageWidth.value = 300;
  cropImageHeight.value = 300;
  // 重置裁剪开关为默认状态
  isCropEnabled.value = false;
};

/**
 * @description 图片上传
 */
const handleHttpUpload = async (options: UploadRequestOptions) => {
  console.log('触发组件上传 handleHttpUpload', options);
  let formData = new FormData();
  formData.append('file', options.file);
  try {
    const api = props.api ?? uploadImg;
    const { data } = await api(formData);
    options.onSuccess(data);
  } catch (error) {
    options.onError(error as any);
  }
};

/**
 * @description 图片上传成功
 */
const uploadSuccess = (responseUrl: string | undefined, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  if (!responseUrl) return;
  uploadFile.url = responseUrl;

  let newImgList = cloneDeep(uploadFiles);
  modelValue.value = newImgList.filter((d) => !!d.url).map((d) => d.url!);

  // 调用 el-form 内部的校验方法
  formItemContext?.prop && formContext?.validateField([formItemContext.prop as string]);
  ElNotification({
    title: '温馨提示',
    message: '图片上传成功！',
    type: 'success'
  });
};

/**
 * @description 删除图片
 */
const handleRemove = (file: UploadFile) => {
  modelValue.value = showFileList.value.filter((item) => item.uid !== file.uid).map((d) => d.url);
};

/**
 * @description 图片上传错误
 */
const uploadError = () => {
  ElNotification({
    title: '温馨提示',
    message: '图片上传失败，请您重新上传！',
    type: 'error'
  });
};

/**
 * @description 文件数超出
 */
const handleExceed = () => {
  ElNotification({
    title: '温馨提示',
    message: `当前最多只能上传 ${props.limit} 张图片，请移除后上传！`,
    type: 'warning'
  });
};

/**
 * @description 图片预览
 */
const viewImageUrl = ref('');
const imgViewVisible = ref(false);
const handlePictureCardPreview: UploadProps['onPreview'] = (file) => {
  viewImageUrl.value = file.url!;
  imgViewVisible.value = true;
};
</script>

<style scoped lang="scss">
.crop-container {
  width: 100%;
  height: 400px;
}
.crop-controls {
  display: flex;
  justify-content: center;
  padding-bottom: 15px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  .crop-switch {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.crop-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}
.dialog-footer {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}
.is-error {
  .upload {
    :deep(.el-upload--picture-card),
    :deep(.el-upload-dragger) {
      border: 1px dashed var(--el-color-danger) !important;
      &:hover {
        border-color: var(--el-color-primary) !important;
      }
    }
  }
}
:deep(.disabled) {
  .el-upload--picture-card,
  .el-upload-dragger {
    cursor: not-allowed;
    background: var(--el-disabled-bg-color) !important;
    border: 1px dashed var(--el-border-color-darker);
    &:hover {
      border-color: var(--el-border-color-darker) !important;
    }
  }
}
.upload-box {
  .no-border {
    :deep(.el-upload--picture-card) {
      border: none !important;
    }
  }
  :deep(.upload) {
    .el-upload-dragger {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      padding: 0;
      overflow: hidden;
      border: 1px dashed var(--el-border-color-darker);
      border-radius: v-bind(borderRadius);
      &:hover {
        border: 1px dashed var(--el-color-primary);
      }
    }
    .el-upload-dragger.is-dragover {
      background-color: var(--el-color-primary-light-9);
      border: 2px dashed var(--el-color-primary) !important;
    }
    .el-upload-list__item,
    .el-upload--picture-card {
      width: v-bind(width);
      height: v-bind(height);
      background-color: transparent;
      border-radius: v-bind(borderRadius);
    }
    .upload-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .upload-handle {
      position: absolute;
      top: 0;
      right: 0;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      cursor: pointer;
      background: rgb(0 0 0 / 60%);
      opacity: 0;
      transition: var(--el-transition-duration-fast);
      .handle-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0 6%;
        color: aliceblue;
        .el-icon {
          margin-bottom: 15%;
          font-size: 140%;
        }
        span {
          font-size: 100%;
        }
      }
    }
    .el-upload-list__item {
      &:hover {
        .upload-handle {
          opacity: 1;
        }
      }
    }
    .upload-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 12px;
      line-height: 30px;
      color: var(--el-color-info);
      .el-icon {
        font-size: 28px;
        color: var(--el-text-color-secondary);
      }
    }
  }
  .el-upload__tip {
    line-height: 15px;
    text-align: center;
  }
}
</style>

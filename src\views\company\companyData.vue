<template>
  <div>
    <el-card header="收入统计">
      <el-row class="py50" :gutter="50" justify="center">
        <el-col class="num-wrap" :span="6">
          <div class="num-val">{{ state.weekIncome }}</div>
          <div class="num-label">本周收益</div>
        </el-col>
        <el-col class="num-wrap" :span="6">
          <div class="num-val">{{ state.monthIncome }}</div>
          <div class="num-label">本月收益</div>
        </el-col>
        <el-col class="num-wrap" :span="6">
          <div class="num-val">{{ state.totalIncome }}</div>
          <div class="num-label">总收益</div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="mt10">
      <template #header>
        公司账户
        <span class="text-12 ml20 text-#666">账户提现需线下联系商务</span>
      </template>
      <el-row class="py50" :gutter="50" justify="center">
        <el-col class="num-wrap" :span="6">
          <div class="num-val">{{ state.balance }}</div>
          <div class="num-label">账户余额</div>
        </el-col>
        <el-col class="num-wrap" :span="6">
          <div class="num-val">{{ state.withdrawnAmount }}</div>
          <div class="num-label">已提现金额</div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script setup name="companyData">
import { reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { companyStatQueryApi } from '@/api/modules/company.js';

const router = useRouter();
const route = useRoute();

const state = reactive({
  balance: 0,
  monthIncome: 0,
  totalIncome: 0,
  weekIncome: 0,
  withdrawnAmount: 0
});
const companyStatQuery = async () => {
  let res = await companyStatQueryApi();
  if (res && !res.hasErrors) {
    Object.assign(state, res.data);
  }
};
companyStatQuery();
</script>
<style scoped lang="scss">
.num-wrap {
  text-align: center;
}
.num-val {
  font-size: 30px;
  font-weight: bold;
  color: #2096f3;
  text-align: center;
}
.num-label {
  font-size: 16px;
}
</style>

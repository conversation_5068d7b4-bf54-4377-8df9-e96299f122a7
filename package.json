{"name": "xiaohu-platform-admin-fed", "private": true, "version": "1.0.0", "type": "module", "description": "xiaohu-platform-admin-fed", "scripts": {"dev": "vite", "serve": "vite", "dev:pro": "vite --mode pro", "build:dev": "vue-tsc && vite build --mode development", "build:test": "vue-tsc && vite build --mode test", "build:test-aliyun": "vite build --mode test", "build:pro": "vue-tsc && vite build --mode production", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "pnpm build:dev && vite preview", "view": "vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "prepare": "husky", "release": "standard-version", "commit": "git add -A && czg && git push"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iconify/json": "^2.2.341", "@iconify/utils": "^2.3.0", "@vueuse/core": "^13.2.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.9.0", "chart.js": "^4.5.0", "dayjs": "^1.11.13", "driver.js": "^1.3.6", "echarts": "^5.6.0", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.10.4", "glob": "^11.0.2", "lodash-es": "^4.17.21", "md5": "^2.3.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "qs": "^6.14.0", "screenfull": "^6.0.2", "sortablejs": "^1.15.6", "spark-md5": "^3.0.2", "unocss": "^66.1.2", "unplugin-auto-import": "^19.2.0", "vue": "^3.5.14", "vue-cropper": "^1.1.4", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/md5": "^2.3.5", "@types/nprogress": "^0.2.3", "@types/qs": "^6.14.0", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@unocss/preset-rem-to-px": "^66.1.2", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "autoprefixer": "^10.4.21", "cz-git": "1.9.2", "czg": "^1.11.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-vue": "^10.1.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "postcss": "^8.5.3", "postcss-html": "^1.8.0", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^6.0.0", "sass": "^1.89.0", "standard-version": "^9.5.0", "stylelint": "^16.19.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-recommended-scss": "^15.0.1", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^15.0.1", "typescript": "^5.8.3", "unplugin-vue-components": "^28.5.0", "unplugin-vue-setup-extend-plus": "^1.0.1", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-pwa": "^1.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.6", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.10"}, "engines": {"node": ">=16.18.0"}, "browserslist": {"production": ["> 1%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "volta": {"node": "18.19.1"}}
import http from '@/api';

/**
 * @name 护工公司相关API
 */
// 护工公司-列表
export const superCompanyListApi = (data) => http.post('/super/admin/company/list', data);

// 护工公司-新增
export const superCompanyCreateApi = (data) => http.post('/super/admin/company/create', data);
// 护工公司-修改信息
export const superCompanyUpdateApi = (data) => {
  return http.post('/super/admin/company/update', data);
};

// 护工公司-删除
export const superCompanyDeleteApi = (params) => {
  return http.delete('/super/admin/company/delete', params);
};

// 护工公司-修改状态
export const superCompanyUpdateStatusApi = (data) => http.post('/super/admin/company/update/status', data);

// 签约医院信息列表
export const superHospitalSelectListApi = (data) => {
  return http.post('/super/admin/hospital/select/list', data);
};

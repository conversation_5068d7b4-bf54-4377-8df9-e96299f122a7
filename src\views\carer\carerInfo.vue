<template>
  <el-card>
    <template #header>
      <div class="fyb">
        <div>{{ titleMap[dialogData.type] || '错误页面，请返回上一页' }}</div>
        <el-button v-if="dialogData.type === 'info'" type="primary" @click="toEdit">修改基本信息</el-button>
      </div>
    </template>
    <el-form ref="formRef" label-width="140px" :model="dialogData.form" :disabled="dialogData.type === 'info'">
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="所属城市" prop="org.cityCode" :rules="dialogData.rules.select">
            <el-select
              :disabled="isInfo"
              @change="cityChange"
              v-model="selectCity"
              filterable
              placeholder="请选择"
              value-key="cityCode">
              <el-option v-for="item in cityList" :key="item.cityCode" :label="item.cityName" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="姓名" prop="name" :rules="dialogData.rules.text">
            <el-input v-model="dialogData.form.name"></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="gender" :rules="dialogData.rules.select">
            <el-radio-group v-model="dialogData.form.gender">
              <el-radio :value="0">男</el-radio>
              <el-radio :value="1">女</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile" :rules="dialogData.rules.mobile">
            <el-input :disabled="isInfo" v-model="dialogData.form.mobile"></el-input>
          </el-form-item>
          <el-form-item label="身份证号" prop="idNumber" :rules="dialogData.rules.text">
            <el-input v-model="dialogData.form.idNumber"></el-input>
            <div class="red-tip">身份证号请对照图片再次核对</div>
          </el-form-item>
          <el-form-item label="年龄" prop="age" :rules="dialogData.rules.text">
            <el-input-number v-model="dialogData.form.age" :min="18" :max="100" controls-position="right" />
          </el-form-item>
          <!-- 籍贯 -->
          <el-form-item label="籍贯" prop="nativePlace" :rules="dialogData.rules.text">
            <el-input v-model="dialogData.form.nativePlace"></el-input>
          </el-form-item>
          <!-- 工作年限 -->
          <el-form-item label="工作年限" prop="workYears" :rules="dialogData.rules.text">
            <el-input-number class="mr10" v-model="dialogData.form.workYears" :min="0" :max="100" controls-position="right" />
            年
            <br />
            <div class="w-full red-tip">依据资质证书发证日期判断</div>
          </el-form-item>
          <el-form-item label="等级" prop="grade" :rules="dialogData.rules.select">
            <el-select v-model="dialogData.form.grade" placeholder="请选择">
              <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <div class="red-tip">依据注册来源、资质证书和工作年限综合判断</div>
          </el-form-item>
          <el-form-item label="资质证书-发证单位" prop="certOrgName">
            <el-input v-model="dialogData.form.certOrgName" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="资质证书-发证日期" prop="certDate">
            <el-input v-model="dialogData.form.certDate" placeholder="如：2025年5月10日"></el-input>
          </el-form-item>
          <el-form-item label="体检机构" prop="healthInfo.checkOrgName" :rules="dialogData.rules.text">
            <el-input v-model="dialogData.form.healthInfo.checkOrgName" placeholder="请输入"></el-input>
            <div class="red-tip">参考体检报告</div>
          </el-form-item>
          <el-form-item label="检测日期" prop="healthInfo.checkDate" :rules="dialogData.rules.text">
            <el-input v-model="dialogData.form.healthInfo.checkDate" placeholder="如：2025年5月10日"></el-input>
            <div class="red-tip">参考体检报告</div>
          </el-form-item>
          <el-form-item label="护工备注" prop="remark">
            <el-input v-model="dialogData.form.remark" :autosize="{ minRows: 4, maxRows: 10 }" type="textarea"></el-input>
          </el-form-item>
          <el-form-item label="信用查询" prop="abnormal">
            <div>
              <div :class="dialogData.form.isNormal === false ? 'text-[--el-color-danger]' : 'text-#0abf89'">
                {{ dialogData.form.abnormal }}
              </div>
              <el-button @click="getCreditReport" type="primary" v-if="dialogData.form.isNormal === false">
                点击查看信用报告
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="个人照" prop="photo" :rules="dialogData.rules.upload">
            <UploadImg enableCrop v-model="dialogData.form.photo"></UploadImg>
          </el-form-item>
          <el-form-item label="身份证照片" prop="idCardImg" :rules="dialogData.rules.uploadList">
            <UploadImgList enableCrop v-model="dialogData.form.idCardImg"></UploadImgList>
          </el-form-item>
          <el-form-item label="资质证书" prop="certImg" :rules="dialogData.rules.uploadList">
            <UploadImgList enableCrop v-model="dialogData.form.certImg"></UploadImgList>
          </el-form-item>
          <el-form-item label="体检报告" prop="healthInfo.healthImg" :rules="dialogData.rules.uploadList">
            <UploadImgList enableCrop v-model="dialogData.form.healthInfo.healthImg"></UploadImgList>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="dialogData.type === 'approve'" class="red-tip text-center">如信息不完善，需要先完善护工信息，再进行审核操作</div>
    <div class="mt40 fc pb40">
      <template v-if="dialogData.type === 'approve'">
        <el-button size="large" type="info" @click="saveForm">保存</el-button>
        <el-button size="large" type="primary" @click="submitForm">同意</el-button>
        <el-button size="large" type="danger" @click="rejectForm">拒绝</el-button>
      </template>
      <template v-else>
        <el-button size="large" @click="back">取 消</el-button>
        <el-button size="large" :disabled="dialogData.type === 'info'" type="primary" @click="submitForm">确 定</el-button>
      </template>
    </div>
    <el-dialog v-model="dialogData.visible" title="信用报告" width="500">
      <JudicialResult :data="dialogData.report"></JudicialResult>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="dialogData.visible">我知道了</el-button>
        </div>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup name="carerDetail" pageName="护工详情">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  superCarerCreateApi,
  superCarerUpdateApi,
  superCarerDetailApi,
  superCarerApproveApi,
  superCreditReportApi
} from '@/api/modules/carer.js';

import UploadImg from '@/components/Upload/Img.vue';
import UploadImgList from '@/components/Upload/Imgs.vue';
import JudicialResult from './components/JudicialResult.vue';
import { areaListApi } from '@/api/modules/system.js';

const route = useRoute();
const router = useRouter();
const formRef = ref();
const selectCity = ref({ cityCode: '', cityName: '' });
const cityList = ref([]);

const typeTitleMap = {
  XHS: '自主/护工邀请注册',
  XHZ: '小护培训营邀请注册',
  DSF: '护工公司邀请码注册'
};

const titleMap = {
  info: '基本信息',
  update: '修改护工基本信息',
  create: '新建自营护工',
  approve: `审核事项：${typeTitleMap[route.query.type] || '错误页面，请返回上一页'}`
};
console.log('route.query.mode', route.query.mode || 'info');
const dialogData = reactive({
  type: route.query.mode,
  form: {
    approvalId: route.query.approvalId,
    age: null,
    certImg: [],
    gender: 1,
    grade: 'JP',
    healthInfo: {
      checkDate: '',
      checkOrgName: '',
      healthImg: []
    },
    certOrgName: '',
    certDate: '',
    idCardImg: [],
    idNumber: '',
    mobile: '',
    name: '',
    nativePlace: '',
    org: {
      cityCode: '',
      cityName: '',
      companyId: '',
      companyName: ''
    },
    photo: '',
    remark: '',
    workYears: null
  },
  rules: {
    text: [{ required: true, message: '请输入', trigger: 'blur' }],
    select: [{ required: true, message: '请选择', trigger: 'blur' }],
    mobile: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
    ],
    upload: [{ required: true, message: '请上传', trigger: 'change' }],
    uploadList: [
      { required: true, message: '请上传', trigger: 'change' },
      { type: 'array', required: true, message: '请至少请上传一张图片', trigger: 'change' }
    ]
  },
  visible: false,
  report: {}
});
const isInfo = computed(() => dialogData.type !== 'create');
const gradeOptions = [
  {
    label: '铜牌陪护师',
    value: 'TP'
  },
  {
    label: '银牌陪护师',
    value: 'YP'
  },
  {
    label: '金牌陪护师',
    value: 'JP'
  }
];
const emit = defineEmits(['saved']);

async function saveForm() {
  let res = await superCarerUpdateApi(dialogData.form);
  console.log('res: ', res);
  if (res && !res.hasErrors) {
    ElMessage.success('保存成功');
    superCarerDetail();
  }
}
async function submitForm() {
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;
  let res = null;
  switch (dialogData.type) {
    case 'update':
      res = await superCarerUpdateApi(dialogData.form);
      break;
    case 'create':
      res = await superCarerCreateApi(dialogData.form);
      break;
    case 'approve':
      dialogData.form.approvalId = route.query.approvalId;
      dialogData.form.approvalStatus = 'pass';
      res = await superCarerApproveApi(dialogData.form);
      break;
    default:
      break;
  }
  console.log('res: ', res);
  if (res && !res.hasErrors) {
    ElMessage.success('操作成功');
    router.back();
  }
}
async function rejectForm() {
  let confirmRes = await ElMessageBox.prompt('', '确定拒绝审核吗?', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputValidator: (value) => {
      if (!value) {
        return '请输入拒绝原因';
      }
      return true;
    },
    inputPlaceholder: '请输入拒绝原因'
  }).catch((e) => false);
  console.log('confirmRes：', confirmRes);
  if (!confirmRes) return;
  let res = await superCarerApproveApi({
    id: route.query.id,
    refusalReason: confirmRes.value,
    approvalId: route.query.approvalId,
    approvalStatus: 'refusal'
  });
  if (res && !res.hasErrors) {
    ElMessage.success('操作成功');
    router.back();
  }
}
async function getCreditReport() {
  let res = await superCreditReportApi({ carerId: dialogData.form.id });
  if (res && !res.hasErrors) {
    console.log('res: ', res);
    dialogData.visible = true;
  }
}
async function areaList() {
  let res = await areaListApi({ level: 0 });
  if (res && !res.hasErrors) {
    cityList.value = res.data.map((d) => ({
      cityCode: d.code,
      cityName: d.name
    }));
  }
}

function cityChange(val) {
  Object.assign(dialogData.form.org, val);
}
const back = () => {
  router.back();
};

const toAdd = () => {
  dialogData.type = 'create';
  dialogData.form.grade = 'JP'; // 默认金牌
  dialogData.form.gender = 1; // 默认女
  dialogData.form.age = null; // 默认空
  dialogData.form.workYears = null; // 默认空
  selectCity.value = { cityCode: '', cityName: '' };
};

const toEdit = () => {
  dialogData.type = 'update';
};
const superCarerDetail = async () => {
  let res = await superCarerDetailApi({
    id: route.query.id
  });
  if (res && !res.hasErrors) {
    selectCity.value = { cityCode: res.data.org.cityCode, cityName: res.data.org.cityName };
    Object.assign(dialogData.form, res.data);
  }
};
onMounted(() => {
  areaList();
  if (dialogData.type !== 'create') {
    superCarerDetail();
  }
});
</script>

<style scoped lang="scss"></style>

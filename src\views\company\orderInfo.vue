<template>
  <div>
    <el-card>
      <template #header>
        <div class="fyc font-bold">
          订单号：{{ state.orderNo }}
          <div class="ml22 px10 py2 text-#ff7f50 border-1 border-solid border-#ff7f50 bdr-4">{{ state.org.cityName }}</div>
          <div class="ml-auto text-#3170ff">{{ orderStatusMap[state.orderStatus] }}</div>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card header="用户信息">
            <el-descriptions border :column="2" label-width="120px">
              <el-descriptions-item label="联系人">{{ state.address.name }} {{ state.address.mobile }}</el-descriptions-item>
              <el-descriptions-item label="患者自理能力">{{ selfCareMap[state.selfCare] }}</el-descriptions-item>
              <el-descriptions-item label="陪护地址" span="2">
                {{ (state.address.locationAds || '') + (state.address.detailAds || '') }}
              </el-descriptions-item>
              <el-descriptions-item label="备注" span="2">{{ state.reason }}</el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card header="预约服务">
            <el-descriptions border :column="1" label-width="120px">
              <el-descriptions-item label="服务产品">{{ state.unit }}</el-descriptions-item>
              <el-descriptions-item label="陪护时间">
                {{ $formatDateTime(state.startTime, 'MM月DD日HH:mm') }}~{{ $formatDateTime(state.endTime, 'MM月DD日HH:mm') }}
                <span class="ml20">{{ state.num }}{{ state.unit }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>
      <el-card class="mt20">
        <template #header>
          <div class="fyb">
            陪护师信息
            <el-button @click="toCarerInfo(state.carerInfo.id)" type="primary" link>
              查看详情信息
              <el-icon class="el-icon--right"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </template>
        <el-descriptions border :column="2" label-width="120px">
          <el-descriptions-item label="姓名">{{ state.carerInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ state.carerInfo.mobile }}</el-descriptions-item>
          <el-descriptions-item label="简介">{{ state.carerInfo.profile }}</el-descriptions-item>
          <el-descriptions-item label="所属护工公司">{{ state.org.companyName }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      <el-card class="mt20" header="交易信息 ">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="text-center mb10 font-bold">账单明细</div>
            <el-descriptions border :column="1" label-width="120px">
              <el-descriptions-item label="陪护服务费">￥{{ state.price }}/天 * {{ state.num }}</el-descriptions-item>
              <el-descriptions-item label="费用总计">￥{{ state.totalPrice }}</el-descriptions-item>
              <el-descriptions-item label="优惠减免">￥{{ state.amount }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="8">
            <div class="text-center mb10 font-bold">支付信息</div>
            <el-descriptions border :column="1" label-width="120px">
              <el-descriptions-item label="支付金额">￥{{ state.taAmount }}</el-descriptions-item>
              <el-descriptions-item label="支付方式">微信支付</el-descriptions-item>
              <el-descriptions-item label="支付时间">{{ $formatDateTime(state.payTime) }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="8">
            <div class="text-center mb10 font-bold">退款信息</div>
            <el-descriptions border :column="1" label-width="120px">
              <el-descriptions-item label="退款金额">
                <template v-if="state.refundAmount > 0">￥{{ state.refundAmount }}</template>
              </el-descriptions-item>
              <el-descriptions-item label="退款时间">{{ $formatDateTime(state.refundTime) }}</el-descriptions-item>
              <el-descriptions-item v-if="state?.reason" label="用户取消原因">
                <el-button type="primary" link @click="openUserReason">查看</el-button>
              </el-descriptions-item>
              <el-descriptions-item v-if="state?.feedback?.reason" label="护工取消原因">
                <el-button type="primary" link @click="dialogData.visible = true">查看</el-button>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
      </el-card>
      <div v-show="tableRecordShow" class="mt20 table-card">
        <ProTable ref="tableRecordRef" :isShowSearchBtn="false" :request-api="superOrderCarerRecordApi" :init-param="filterData">
          <template #tableHeader>陪护记录</template>
          <el-table-column type="index" label="序号" width="55"></el-table-column>
          <el-table-column width="170" prop="nurseDate" label="日期"></el-table-column>
          <el-table-column min-width="100" prop="items" label="陪护事项"></el-table-column>
          <el-table-column min-width="90" prop="desc" label="陪护说明"></el-table-column>
          <el-table-column width="120" prop="operator" label="操作人"></el-table-column>
        </ProTable>
      </div>
      <div v-show="tableCommentShow" class="mt20 table-card">
        <ProTable ref="tableCommentRef" :isShowSearchBtn="false" :request-api="superOrderCommentListApi" :init-param="filterData">
          <template #tableHeader>服务评价</template>
          <el-table-column type="index" label="序号" width="55"></el-table-column>
          <el-table-column width="250" prop="avatarUrl" label="用户信息">
            <template #default="scope">
              <div>{{ scope.row.nickName }}：{{ commentTypeMap[scope.row.type] }}</div>
              <el-image class="w50 h50" :src="scope.row.avatarUrl" fit="contain" />
            </template>
          </el-table-column>
          <el-table-column min-width="90" prop="content" label="评价内容"></el-table-column>
          <el-table-column
            width="170"
            prop="createdTime"
            label="评价时间"
            :formatter="(row) => $formatDateTime(row.createdTime)"></el-table-column>
        </ProTable>
      </div>
      <div class="mt20 table-card">
        <ProTable :isShowSearchBtn="false" :request-api="superOrderOprListApi" :init-param="filterData">
          <template #tableHeader>订单操作日志</template>
          <el-table-column type="index" label="序号" width="55"></el-table-column>
          <el-table-column
            min-width="90"
            prop="createdTime"
            label="日期"
            :formatter="(row) => $formatDateTime(row.createdTime)"></el-table-column>
          <el-table-column min-width="90" prop="content" label="事项"></el-table-column>
          <el-table-column min-width="90" prop="operatorName" label="操作人"></el-table-column>
        </ProTable>
      </div>
    </el-card>
    <el-dialog :title="dialogData.title" v-model="dialogData.visible" width="600px">
      <el-form-item label-position="right" label-width="100px" label="反馈原因：">{{ state?.feedback?.reason }}</el-form-item>
      <el-form-item label-position="right" label-width="100px" label="详细原因：">{{ state?.feedback?.desc }}</el-form-item>
      <el-form-item label-position="right" label-width="100px" label="反馈图片：">
        <div class="flex flex-wrap gap10">
          <el-image
            v-for="(item, index) in state?.feedback?.images || []"
            :preview-src-list="state?.feedback?.images || []"
            :initial-index="index"
            :key="item"
            class="w100 h100"
            :src="item"
            fit="contain" />
        </div>
      </el-form-item>
    </el-dialog>
  </div>
</template>
<script setup name="companyOrderInfo">
import { reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
const router = useRouter();
const route = useRoute();
import {
  superOrderQueryApi,
  superOrderCarerRecordApi,
  superOrderCommentListApi,
  superOrderOprListApi
} from '@/api/modules/order.js';
// 订单状态映射
const orderStatusMap = {
  wait_pay: '待支付',
  pay_off: '待接单',
  wait_visit: '待上门',
  in_service: '服务中',
  cancel: '已取消',
  done: '已完成'
};
// 评价类型 A 太赞了 B 一般般 C 不满意
const commentTypeMap = {
  A: '太赞了',
  B: '一般般',
  C: '不满意'
};
// 自理能力 A 完全自理 B 部分自理 C 不可自理
const selfCareMap = {
  A: '完全自理',
  B: '部分自理',
  C: '不可自理'
};
// 表格搜索数据
const filterData = reactive({
  orderNo: route.query.orderNo || ''
});
// 订单信息
// 默认数据为空
const state = reactive({
  address: {
    detailAds: '',
    location: [],
    locationAds: '',
    mobile: '',
    name: ''
  },
  amount: 0,
  carerInfo: {
    companyId: '',
    companyName: '',
    id: '',
    mobile: '',
    name: '',
    profile: ''
  },
  endTime: '',
  endTime: '',
  feedback: {
    desc: '',
    images: [],
    reason: ''
  },
  num: 0,
  orderNo: '',
  orderStatus: '',
  org: {
    cityCode: '',
    cityName: '',
    companyId: '',
    companyName: ''
  },
  payTime: '',
  price: 0,
  reason: '',
  refundAmount: 0,
  refundTime: '',
  selfCare: '',
  startTime: '',
  taAmount: 0,
  totalPrice: 0,
  unit: ''
});
const dialogData = reactive({
  visible: false,
  title: '护工取消原因'
});
const toCarerInfo = (id) => {
  router.push({ path: '/company/carerInfo', query: { id, mode: 'info' } });
};

const openUserReason = () => {
  ElMessageBox.alert(state.reason, '用户取消原因', {
    confirmButtonText: '我知道了'
  });
};

const tableRecordRef = ref(null);
const tableRecordShow = computed(() => {
  return tableRecordRef?.value?.tableData?.length > 0;
});

const tableCommentRef = ref(null);
const tableCommentShow = computed(() => {
  return tableCommentRef?.value?.tableData?.length > 0;
});

const superOrderQuery = async () => {
  let res = await superOrderQueryApi({
    orderNo: route.query.orderNo
  });
  if (res && !res.hasErrors) {
    Object.assign(state, res.data);
  }
};
onMounted(() => {
  superOrderQuery();
});
</script>
<style scoped lang="scss">
.table-card {
  box-shadow: var(--el-box-shadow-light);
}
:deep(.el-row) {
  .el-col {
    height: auto;
    .el-card {
      height: 100%;
    }
  }
}
</style>

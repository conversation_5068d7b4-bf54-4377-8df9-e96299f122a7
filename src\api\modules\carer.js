import http from '@/api';

// 护工中心-护工列表
export const superCarerListApi = (data) => {
  return http.post('/super/admin/carer/list', data);
};

// 护工中心-护工详情
export const superCarerDetailApi = (params) => {
  return http.post('/super/admin/carer/detail', {}, { params });
};

// 护工中心-护工信息保存
export const superCarerUpdateApi = (data) => {
  return http.post('/super/admin/carer/update', data);
};

// 护工中心-新增自营护工
export const superCarerCreateApi = (data) => {
  return http.post('/super/admin/carer/create', data);
};

// 护工中心-护工信息审批
export const superCarerApproveApi = (data) => {
  return http.post('/super/admin/carer/approve', data);
};

// 护工中心-待审核护工列表
export const superCarerWaitListApi = (data) => {
  return http.post('/super/admin/carer/wait/list', data);
};

// 护工中心-修改登录账号状态
export const superCarerUpdateStatusApi = (data) => {
  return http.post('/super/admin/carer/update/status', data);
};

// 护工中心-删除护工
export const superCarerDeleteApi = (params) => {
  return http.delete('/super/admin/carer/delete', params);
};

// 查询护工信用报告
export const superCreditReportApi = (params) => {
  return http.get('/super/admin/carer/credit/report/v2', params);
};

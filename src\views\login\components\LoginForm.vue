<template>
  <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large">
    <el-form-item prop="mobile">
      <el-input v-model="loginForm.mobile" placeholder="请输入手机号">
        <template #prefix>
          <el-icon class="el-input__icon">
            <user />
          </el-icon>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="code">
      <el-input v-model="loginForm.code" placeholder="请输入验证码" maxlength="6">
        <template #prefix>
          <el-icon class="el-input__icon">
            <lock />
          </el-icon>
        </template>
        <template #append>
          <el-button @click="getCode" :disabled="codeDisabled">{{ codeMessage }}</el-button>
        </template>
      </el-input>
    </el-form-item>
  </el-form>
  <div class="login-btn">
    <el-button :icon="CircleClose" round size="large" @click="resetForm(loginFormRef)">重置</el-button>
    <el-button :icon="UserFilled" round size="large" type="primary" :loading="loading" @click="login(loginFormRef)">
      登录
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { HOME_URL } from '@/config';
import { getTimeState } from '@/utils';
import { ElNotification, ElMessage } from 'element-plus';
import { loginApi, securityCodeApi } from '@/api/modules/login';
import { useUserStore } from '@/stores/modules/user';
import { useTabsStore } from '@/stores/modules/tabs';
import { useKeepAliveStore } from '@/stores/modules/keepAlive';
import { initDynamicRouter } from '@/routers/modules/dynamicRouter';
import { CircleClose, UserFilled } from '@element-plus/icons-vue';
import type { ElForm } from 'element-plus';
import { Login } from '@/api/interface';

const router = useRouter();
const userStore = useUserStore();
const tabsStore = useTabsStore();
const keepAliveStore = useKeepAliveStore();

type FormInstance = InstanceType<typeof ElForm>;
const loginFormRef = ref<FormInstance>();
const loginRules = reactive({
  mobile: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
});

const loading = ref(false);
const loginForm = reactive<Login.ReqLoginForm>({
  mobile: window.localStorage.getItem('my-platform-userLogin-Mobile') || '',
  code: ''
});

const codeDisabled = ref(false);
const codeMessage = ref('获取验证码');
let timer: any = null;
let count = ref(0);

const codeTime = () => {
  const TIME_COUNT = 60;
  count.value = TIME_COUNT;
  codeDisabled.value = true;
  if (!timer) {
    timer = setInterval(() => {
      if (count.value > 0 && count.value <= TIME_COUNT) {
        codeMessage.value = count.value + '秒后重新获取';
        count.value--;
      } else {
        codeDisabled.value = false;
        clearInterval(timer);
        codeMessage.value = '获取验证码';
        timer = null;
      }
    }, 1000);
  }
};

const getCode = async () => {
  if (!loginForm.mobile) {
    ElMessage.error('请输入手机号');
    return;
  }
  try {
    await securityCodeApi({ mobile: loginForm.mobile });
    ElMessage.success('验证码已发送');
    codeTime();
  } catch (e) {
    // 错误处理
  }
};

const login = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async (valid) => {
    if (!valid) return;
    loading.value = true;
    try {
      // 1.执行登录接口
      const { data } = await loginApi({ ...loginForm });
      userStore.setUserInfo(data);
      userStore.setToken(data.token);
      window.localStorage.setItem('my-platform-userLogin-Mobile', loginForm.mobile);
      // 2.添加动态路由
      await initDynamicRouter();
      // 3.清空 tabs、keepAlive 数据
      tabsStore.setTabs([]);
      keepAliveStore.setKeepAliveName([]);
      // 4.跳转到首页
      router.push(HOME_URL);
      ElNotification({
        title: getTimeState(),
        message: '欢迎登录 Admin',
        type: 'success',
        duration: 3000
      });
    } finally {
      loading.value = false;
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};

onMounted(() => {
  document.onkeydown = (e: KeyboardEvent) => {
    if (e.code === 'Enter' || e.code === 'enter' || e.code === 'NumpadEnter') {
      if (loading.value) return;
      login(loginFormRef.value);
    }
  };
});

onBeforeUnmount(() => {
  document.onkeydown = null;
});
</script>

<style scoped lang="scss">
@use '../index';
</style>

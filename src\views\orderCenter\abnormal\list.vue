<template>
  <div class="table-box">
    <!-- tab 待处理，已处理 -->
    <el-tabs v-model="activeName" type="border-card">
      <el-tab-pane label="待处理" name="wait">
        <abnormalList status="wait" />
      </el-tab-pane>
      <el-tab-pane label="已处理" name="done" lazy>
        <abnormalList status="done" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup name="abnormalOrderList" pageName="异常订单列表">
import abnormalList from './components/abnormalList.vue';
import { ref } from 'vue';
const activeName = ref('wait');
</script>
<style scoped lang="scss">
:deep(.el-tabs) {
  flex: 1;
  .el-tab-pane {
    height: 100%;
  }
}
</style>

export const usrDateRangeValue = (params, dateKeyName = ['startDate', 'endDate']) => {
  const dateRangeValue = ref([]);
  const dateRangeChange = (changeValue) => {
    console.log('params：', params);
    console.log('dateKeyName：', dateKeyName);
    console.log('changeValue：', changeValue);
    if (changeValue && changeValue.length > 0) {
      params[dateKeyName[0]] = changeValue[0];
      params[dateKeyName[1]] = changeValue[1];
    } else {
      params[dateKeyName[0]] = '';
      params[dateKeyName[1]] = '';
    }
  };
  return [dateRangeValue, dateRangeChange];
};

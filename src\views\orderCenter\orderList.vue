<template>
  <div class="table-box">
    <ProTable ref="tableRef" :request-api="superOrderListApi" :init-param="filterData">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-form-item label="用户手机号">
          <el-input v-model="filterData.mobile" type="tel" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="filterData.orderNo" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="所属护工公司">
          <SelectCompany v-model="filterData.companyId"></SelectCompany>
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select clearable v-model="filterData.orderStatus" placeholder="请选择">
            <el-option v-for="item in orderStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </template>
      <el-table-column type="index" label="序号" width="55"></el-table-column>
      <el-table-column min-width="90" prop="org.cityName" label="城市"></el-table-column>
      <el-table-column min-width="300" label="用户信息">
        <template #default="scope">
          <div>{{ scope.row.address.name }} {{ scope.row.address.mobile }}</div>
          <div>{{ (scope.row.address.locationAds || '') + (scope.row.address.detailAds || '') }}</div>
        </template>
      </el-table-column>
      <el-table-column min-width="150" label="护工信息">
        <template #default="scope">
          <div>{{ scope.row.carerInfo.name }} {{ scope.row.carerInfo.mobile }}</div>
          <div>{{ scope.row.carerInfo.profile || '' }}</div>
        </template>
      </el-table-column>
      <el-table-column min-width="300" label="服务信息">
        <template #default="scope">
          <div>产品：{{ scope.row.unit }}</div>
          <div>
            陪护时间：{{ $formatDateTime(scope.row.startTime, 'MM月DD日HH:mm') }}~{{
              $formatDateTime(scope.row.endTime, 'MM月DD日HH:mm')
            }}
          </div>
          <div>{{ scope.row.num }}{{ scope.row.unit }}</div>
        </template>
      </el-table-column>
      <el-table-column width="280" label="支付信息">
        <template #default="scope">
          <div>订单编号：{{ scope.row.orderNo }}</div>
          <div>费用总计：{{ scope.row.totalPrice }}元</div>
          <div>支付金额：{{ scope.row.taAmount }}元</div>
          <div>支付时间：{{ $formatDateTime(scope.row.payTime) }}</div>
        </template>
      </el-table-column>
      <el-table-column
        width="100"
        label="订单状态"
        :formatter="({ orderStatus }) => orderStatusMap[orderStatus]"></el-table-column>
      <el-table-column
        width="170"
        prop="createTime"
        label="创建时间"
        :formatter="({ createdTime }) => $formatDateTime(createdTime)"></el-table-column>
      <el-table-column fixed="right" width="100" label="操作">
        <template #default="scope">
          <el-button type="primary" @click="toOrderInfo(scope.row.orderNo)">详情</el-button>
          <!-- <el-button v-if="['pay_off', 'wait_visit'].includes(scope.row.orderStatus)" type="danger" @click="toOpen(scope.row)">
            退款
          </el-button>
          <el-button v-if="['in_service'].includes(scope.row.orderStatus)" type="danger" @click="toOpen(scope.row)">
            终止
          </el-button> -->
        </template>
      </el-table-column>
    </ProTable>
    <el-dialog v-model="dialogData.visible" :title="dialogData.title" width="500px">
      <el-form :model="dialogData.form" :rules="dialogData.rules" ref="formRef" label-width="120px">
        <el-form-item label="免打扰手机号" prop="phone" :rules="dialogData.rules.phone">
          <el-input v-model="dialogData.form.phone" type="tel" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="添加原因" prop="blackDesc" :rules="dialogData.rules.text">
          <el-input v-model="dialogData.form.blackDesc" placeholder="请输入" clearable></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogData.visible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="orderList" pageName="订单列表">
import { reactive, ref } from 'vue';
import { superOrderListApi } from '@/api/modules/order.js';
import SelectCompany from '@/components/SelectCompany/index.vue';
const router = useRouter();

// 订单状态映射
const orderStatusMap = {
  wait_pay: '待支付',
  pay_off: '待接单',
  wait_visit: '待上门',
  in_service: '服务中',
  cancel: '已取消',
  done: '已完成'
};

// 订单状态选项
const orderStatusOptions = [
  { label: '待支付', value: 'wait_pay' },
  { label: '待接单', value: 'pay_off' },
  { label: '待上门', value: 'wait_visit' },
  { label: '服务中', value: 'in_service' },
  { label: '已取消', value: 'cancel' },
  { label: '已完成', value: 'done' }
];

// 表格搜索数据
const filterData = reactive({
  mobile: '',
  orderNo: '',
  companyId: '',
  orderStatus: ''
});
// ProTable 实例
const tableRef = ref();

// 弹窗组件功能
const formRef = ref();
const dialogData = reactive({
  visible: false,
  title: 'xxx',
  form: {},
  rules: {
    text: [{ required: true, message: '请输入', trigger: 'blur' }],
    upload: [{ required: true, message: '请上传', trigger: 'change' }],
    select: [{ required: true, message: '请选择', trigger: 'change' }]
  }
});

const toOrderInfo = (orderNo) => {
  router.push({
    path: '/orderCenter/orderInfo',
    query: { orderNo }
  });
};
</script>
<style scoped lang="scss"></style>

const modules = import.meta.glob('@/assets/icons/**/*.svg', { eager: true });

const icons = Object.keys(modules)
  .map((path) => {
    // 兼容 Windows 路径分隔符，提取出 `menu/zujian.svg`
    const match = path.match(/icons[\\/](.+?)\.svg$/);
    if (!match) return '';

    const fullName = match[1]; // 例如 "menu/zujian"
    const parts = fullName.split(/[\\/]/); // => ['menu', 'zujian']

    if (parts.length !== 2) return ''; // 忽略不符合格式的
    const [folder, name] = parts;
    return {
      collections: folder,
      name: name,
      path: `${folder}-${name}`
    }; // => menu-zujian
  })
  .filter(Boolean);

export default icons;

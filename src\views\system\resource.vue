<template>
  <div class="table-box">
    <!--表格渲染-->
    <ProTable
      :isShowSearchBtn="false"
      ref="tableRef"
      default-expand-all
      :data="list"
      :requestApi="loadMenus"
      :requestAuto="false"
      border
      row-key="mid">
      <template #tableHeaderButton>
        <el-button type="primary" :icon="Plus" @click="toAdd">新增菜单</el-button>
      </template>
      <el-table-column type="index" label="序号" width="55"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" label="菜单标题" prop="title" />
      <el-table-column prop="icon" label="图标" align="center" width="60px">
        <template #default="scope">
          <SvgIcon :name="scope.row.icon || ''" :iconStyle="{ height: '30px', width: '16px', fill: 'currentColor' }" />
        </template>
      </el-table-column>
      <el-table-column prop="menuSort" align="center" label="排序"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="path" label="路由地址" />
      <el-table-column :show-overflow-tooltip="true" prop="component" label="组件路径" />
      <el-table-column prop="iframe" label="外链" width="75px">
        <template #default="scope">
          <span v-if="scope.row.iframe">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="hidden" label="可见" width="75px">
        <template #default="scope">
          <span v-if="scope.row.hidden">否</span>
          <span v-else>是</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="180px" align="center" fixed="right">
        <template #default="scope">
          <el-button v-if="scope.row.type === 0" type="primary" :icon="Plus" @click="toAdd({ type: 1, pid: scope.row.mid })">
            新增菜单
          </el-button>
          <el-button type="primary" :icon="Edit" @click="editMenu(scope.row)">编辑</el-button>
          <el-button type="danger" :icon="Delete" @click="deleteRow(scope.row.mid, scope)">删除</el-button>
        </template>
      </el-table-column>
    </ProTable>
    <!--表单渲染-->
    <el-dialog append-to-body :close-on-click-modal="false" v-model="menuVisible" :title="dialogTitle" width="600px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="菜单类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio-button :label="0">目录</el-radio-button>
            <el-radio-button :label="1">菜单</el-radio-button>
            <el-radio-button :label="2">按钮</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.type === 1" label="上级类目" prop="pid">
          <el-tree-select
            v-model="form.pid"
            :data="menus"
            :props="treeSelectProps"
            style="width: 460px"
            placeholder="选择上级类目"
            check-strictly
            :render-after-expand="false"
            :default-expand-all="true"
            :no-data-text="'暂无数据'" />
        </el-form-item>
        <el-form-item v-show="form.type !== 2" label="菜单图标" prop="icon">
          <el-popover placement="bottom-start" width="460" trigger="click">
            <template #reference>
              <el-input v-model="form.icon" placeholder="点击选择图标" readonly>
                <template #prefix>
                  <SvgIcon
                    v-if="form.icon"
                    :name="form.icon"
                    class="el-input__icon"
                    :iconStyle="{ height: '30px', width: '16px', fill: 'currentColor' }" />
                  <i v-else class="el-icon-search el-input__icon" />
                </template>
              </el-input>
            </template>
            <IconSelect ref="iconSelectRef" @selected="selected" />
          </el-popover>
        </el-form-item>
        <el-form-item v-show="form.type !== 2" label="菜单标题" prop="title">
          <el-input v-model="form.title" placeholder="菜单标题" />
        </el-form-item>
        <el-form-item v-show="form.type === 2" label="按钮名称" prop="name">
          <el-input v-model="form.name" placeholder="按钮名称" />
        </el-form-item>
        <el-form-item v-if="form.type !== 2" label="路由地址" prop="path">
          <el-input v-model="form.path" placeholder="路由地址" />
        </el-form-item>
        <el-form-item v-show="!form.iframe && form.type !== 2" label="组件路径" prop="component">
          <el-input v-model="form.component" placeholder="组件路径" />
        </el-form-item>
        <el-form-item label="菜单排序" prop="menuSort">
          <el-input-number v-model="form.menuSort" :min="0" :max="999" controls-position="right" />
        </el-form-item>
        <!-- <el-form-item v-show="!form.iframe && form.type !== 2" label="组件名称" prop="componentName">
          <el-input v-model="form.componentName" placeholder="匹配组件内Name字段" />
        </el-form-item> -->
        <el-form-item v-show="form.type !== 2" label="外链菜单" prop="iframe">
          <el-radio-group v-model="form.iframe">
            <el-radio-button :label="true">是</el-radio-button>
            <el-radio-button :label="false">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="form.type !== 2" label="菜单可见" prop="hidden">
          <el-radio-group v-model="form.hidden">
            <el-radio-button :label="false">是</el-radio-button>
            <el-radio-button :label="true">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="form.type !== 0" label="权限标识" prop="permission">
          <el-input v-model="form.permission" :disabled="form.iframe" placeholder="权限标识" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="text" @click="menuVisible = false">取消</el-button>
        <el-button type="primary" @click="save">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="resource">
import { ref, reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import {
  resourceListApi,
  superSysMenusLazyApi,
  superSysMenusCreateApi,
  superSysMenusUpdateApi,
  superSysMenusDeleteApi
} from '@/api/modules/system.js';
import IconSelect from '@/components/IconSelect/index.vue';
import SvgIcon from '@/components/SvgIcon/index.vue';
import { Plus, Delete, Edit } from '@element-plus/icons-vue';
const formRef = ref();
const tableRef = ref();
const iconSelectRef = ref();

const dialogTitle = ref('新增菜单');
const menuVisible = ref(false);
const form = reactive({
  id: null,
  name: null,
  sort: null,
  path: null,
  component: null,
  componentName: null,
  iframe: false,
  roles: [],
  pid: 0,
  icon: null,
  hidden: false,
  type: 0,
  permission: null,
  menuSort: 0,
  title: ''
});
const rules = {
  title: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  path: [{ required: true, message: '请输入地址', trigger: 'blur' }]
};
const list = ref([]);
const menus = ref([]);

const treeSelectProps = {
  value: 'id',
  label: 'label',
  children: 'children'
};

function resetIconSelect() {
  iconSelectRef.value && iconSelectRef.value.reset();
}

function selected(name) {
  form.icon = name;
}

function toAdd(row = {}) {
  dialogTitle.value = '新增菜单';
  console.log('row: ', row);
  Object.assign(form, {
    id: null,
    name: null,
    sort: null,
    path: null,
    component: null,
    componentName: null,
    iframe: false,
    roles: [],
    pid: 0,
    icon: null,
    hidden: false,
    type: 0,
    permission: null,
    menuSort: 0,
    title: '',
    ...row
  });
  console.log('form: ', form);
  menuVisible.value = true;
  resetIconSelect();
}

function editMenu(row) {
  dialogTitle.value = '编辑菜单';
  Object.assign(form, { ...row });
  menuVisible.value = true;
  resetIconSelect();
}

async function deleteRow(mid) {
  console.log('mid: ', mid);
  const confirmRes = await ElMessageBox.confirm('确定删除此菜单吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).catch(() => false);
  if (!confirmRes) return;
  const res = await superSysMenusDeleteApi({ mid });
  if (res && !res.hasErrors) {
    ElMessage.success('操作成功');
    loadMenus();
  }
}

async function save() {
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;
  let res = null;
  if (dialogTitle.value === '编辑菜单') {
    res = await superSysMenusUpdateApi(form);
  } else {
    res = await superSysMenusCreateApi(form);
  }
  if (res && !res.hasErrors) {
    ElMessage.success('操作成功');
    menuVisible.value = false;
    loadMenus();
  }
}

async function loadMenus() {
  const res = await superSysMenusLazyApi();
  if (res && !res.hasErrors) {
    // 处理菜单数据
    const parseMenus = (menu) => {
      menu.id = menu.mid;
      if (menu.hasChildren) {
        // table 表格渲染子级，需要删除 hasChildren
        delete menu.hasChildren;
        menu.children.forEach((d) => {
          parseMenus(d);
        });
      } else {
        delete menu.children;
      }
    };

    // 处理所有菜单数据
    res.data.forEach((d) => {
      parseMenus(d);
    });

    // 更新列表数据
    list.value = res.data;

    // 构建树形选择器数据
    const convert = (arr) =>
      arr.map((item) => ({
        id: item.mid,
        label: item.title || item.name || '未命名',
        children: item.children ? convert(item.children) : [],
        hasChildren: item.children ? true : false
      }));

    menus.value = [{ id: 0, label: '顶级类目', children: convert(res.data) }];

    // 刷新表格布局
    tableRef.value && tableRef.value.doLayout && tableRef.value.doLayout();
  }
}

onMounted(() => {
  loadMenus();
});
</script>

<template>
  <div class="table-box">
    <!--表格渲染-->
    <ProTable ref="tableRef" :requestApi="superCarerWaitListApi" :init-param="filterData">
      <template #tableHeader>
        <el-form inline>
          <el-form-item label="姓名:">
            <el-input clearable v-model="filterData.name"></el-input>
          </el-form-item>
          <el-form-item label="手机号:">
            <el-input clearable v-model="filterData.mobile"></el-input>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select v-model="filterData.type" placeholder="请选择" clearable>
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属护工公司" prop="companyId">
            <el-select
              v-model="filterData.companyId"
              clearable
              filterable
              remote
              remote-show-suffix
              reserve-keyword
              placeholder="请搜索"
              :remote-method="remoteMethod"
              :loading="state.loading">
              <el-option v-for="item in state.companyList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
      </template>
      <el-table-column type="index" label="序号" width="55"></el-table-column>
      <el-table-column width="90" prop="name" label="姓名"></el-table-column>
      <el-table-column width="130" prop="mobile" label="手机号"></el-table-column>
      <el-table-column width="80" prop="gender" label="性别" :formatter="(row) => sexMap[row.gender] || ''"></el-table-column>
      <el-table-column width="80" prop="age" label="年龄"></el-table-column>
      <el-table-column width="90" prop="type" label="类型" :formatter="(row) => typeMap[row.type] || ''"></el-table-column>
      <el-table-column width="130" prop="approvalItem" label="审核事项"></el-table-column>
      <el-table-column width="90" prop="org.cityName" label="所属城市"></el-table-column>
      <el-table-column min-width="150" prop="org.companyName" label="所属护工公司"></el-table-column>
      <el-table-column
        width="170"
        prop="createdTime"
        label="创建时间"
        :formatter="({ createdTime }) => $formatDateTime(createdTime)"></el-table-column>
      <el-table-column label="操作" width="150px" align="center" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            :icon="Edit"
            @click="toCarerInfo('approve', { id: scope.row.id, type: scope.row.type, approvalId: scope.row.approvalId })">
            去审核
          </el-button>
        </template>
      </el-table-column>
    </ProTable>
  </div>
</template>

<script setup name="carerWaitList">
import { ref, reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { superCarerWaitListApi } from '@/api/modules/carer.js';
import { superCompanyListApi } from '@/api/modules/systemCompany.js';

import { Plus, Delete, Edit } from '@element-plus/icons-vue';
import router from '@/routers';
const tableRef = ref();
const filterData = reactive({
  name: '',
  mobile: '',
  type: '',
  grade: '',
  companyId: ''
});
const statusColorMap = {
  study: 'text-#fa2400',
  free: 'text-#0db817',
  run: 'text-#ff7f50',
  stop: 'text-#fa2400'
};
const statusMap = {
  study: '待培训',
  free: '接单中',
  run: '服务中',
  stop: '已暂停'
};
const userStatusMap = {
  normal: '正常',
  disabled: '已暂停'
};
const sexMap = {
  0: '男',
  1: '女'
};

const typeOptions = [
  {
    label: '平台签约',
    value: 'XHS'
  },
  {
    label: '平台自营',
    value: 'XHZ'
  },
  {
    label: '第三方',
    value: 'DSF'
  }
];
const typeMap = {
  XHS: '平台签约',
  XHZ: '平台自营',
  DSF: '第三方'
};
const gradeOptions = [
  {
    label: '铜牌陪护师',
    value: 'TP'
  },
  {
    label: '银牌陪护师',
    value: 'YP'
  },
  {
    label: '金牌陪护师',
    value: 'JP'
  }
];
const gradeMap = {
  TP: '铜牌陪护师',
  YP: '银牌陪护师',
  JP: '金牌陪护师'
};

const companyIdOptions = ref([]);
const rules = {
  title: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  path: [{ required: true, message: '请输入地址', trigger: 'blur' }]
};

const state = reactive({
  loading: false,
  companyList: []
});
const superCompanyList = async (companyName) => {
  let res = await superCompanyListApi({
    page: 1,
    pageSize: 10,
    companyName
  });
  state.loading = false;
  if (res && !res.hasErrors) {
    state.companyList = res.data.content.map((d) => {
      return {
        label: d.companyName,
        value: d.id
      };
    });
  }
};
const remoteMethod = (query) => {
  state.loading = true;
  superCompanyList(query || '');
};
superCompanyList('');
const toCarerInfo = (mode, data = {}) => {
  router.push({ path: '/carer/carerInfo', query: { mode, ...data } });
};
const toAdd = () => {};
const editMenu = () => {};
const deleteRow = () => {};
</script>

<template>
  <div class="table-box">
    <ProTable ref="tableRef" :requestApi="superCompanyListApi" :init-param="filterData">
      <template #tableHeader>
        <el-form inline>
          <el-form-item label="公司名称:">
            <el-input clearable v-model="filterData.companyName"></el-input>
          </el-form-item>
          <el-form-item label="手机号:">
            <el-input clearable v-model="filterData.contactMobile"></el-input>
          </el-form-item>
        </el-form>
      </template>
      <template #tableHeaderButton>
        <el-button type="primary" :icon="Plus" @click="add">添加企业</el-button>
      </template>
      <el-table-column type="index" label="序号" width="55"></el-table-column>
      <el-table-column width="90" prop="cityName" label="所属城市"></el-table-column>
      <el-table-column min-width="90" prop="companyName" label="护工公司"></el-table-column>
      <el-table-column width="100" prop="contactName" label="联系人姓名"></el-table-column>
      <el-table-column width="120" prop="contactMobile" label="联系人手机号"></el-table-column>
      <el-table-column min-width="90" prop="hospitals" label="签约医院">
        <template #default="scope">
          {{ scope.row.hospitalNames?.join('、') }}
        </template>
      </el-table-column>
      <el-table-column width="90" label="账号状态" :formatter="(row) => statusOptions[row.status]"></el-table-column>
      <el-table-column width="120" prop="accountBalance" label="账户余额"></el-table-column>
      <el-table-column
        width="170"
        prop="createdTime"
        label="创建时间"
        :formatter="({ createdTime }) => $formatDateTime(createdTime)"></el-table-column>
      <el-table-column label="操作" width="230px" fixed="right">
        <template #default="props">
          <el-button type="primary" @click="toUpdate(props.row)">修改</el-button>
          <template v-if="props.row.status === 'enabled'">
            <el-button type="danger" @click="changeStatus(props.row.id, 'disabled', '停用')">停用</el-button>
          </template>
          <template v-if="props.row.status === 'disabled'">
            <el-button type="primary" @click="changeStatus(props.row.id, 'enabled', '启用')">启用</el-button>
            <el-button type="danger" @click="toDelete(props.row.id)">删除</el-button>
          </template>
        </template>
      </el-table-column>
    </ProTable>
    <companyDetailVue @saved="refreshDataList" ref="companyDetailVueRef" :close-on-click-modal="false" />
  </div>
</template>

<script setup name="companyList" pageName="护工公司">
import { ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Plus, Delete, Edit } from '@element-plus/icons-vue';
import { cloneDeep } from 'lodash-es';

import { superCompanyListApi, superCompanyUpdateStatusApi, superCompanyDeleteApi } from '@/api/modules/systemCompany.js';
import companyDetailVue from './components/companyDetail.vue';

const filterData = reactive({
  companyName: '',
  contactMobile: ''
});
const statusOptions = {
  enabled: '正常',
  disabled: '停用'
};
const { tableRef, refreshDataList } = useTableDom('tableRef');
const companyDetailVueRef = ref();

function changeStatus(id, status, statusTip) {
  ElMessageBox.confirm(`确定要${statusTip}此公司吗?`, '操作提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    let res = await superCompanyUpdateStatusApi({ id, status });
    if (res && !res.hasErrors) {
      refreshDataList();
      ElMessage.success('操作成功');
    }
  });
}

function add() {
  companyDetailVueRef.value.toAdd();
}

function toUpdate(row) {
  companyDetailVueRef.value.toEdit(cloneDeep(row));
}

const toDelete = async (id) => {
  await useHandleData(superCompanyDeleteApi, { id }, '是否删除?');
  refreshDataList();
};
</script>

<style scoped lang="scss">
.logo-img {
  width: 100px;
}
</style>

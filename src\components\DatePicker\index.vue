<template>
  <el-date-picker
    v-model="dateRangeValue"
    @change="dateRangeChange"
    type="daterange"
    placeholder="请选择"
    start-placeholder="开始日期"
    range-separator="至"
    end-placeholder="结束日期"
    :default-time="[new Date(2024, 9, 1, 0, 0, 0), new Date(2024, 9, 1, 23, 59, 59)]"
    v-bind="$attrs"></el-date-picker>
</template>
<script setup name="DatePicker">
import { usrDateRangeValue } from '@/hooks/index.js';

const props = defineProps({
  value: {
    type: Object,
    required: true
  },
  dateKeyName: {
    type: Array,
    default: () => ['startDate', 'endDate']
  }
});
const [dateRangeValue, dateRangeChange] = usrDateRangeValue(props.value, props.dateKeyName);
defineExpose({
  dateRangeValue,
  dateRangeChange
});
</script>
<style scoped lang="scss"></style>

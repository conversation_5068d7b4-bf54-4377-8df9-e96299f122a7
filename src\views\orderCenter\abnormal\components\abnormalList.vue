<template>
  <div class="table-box">
    <ProTable ref="tableRef" :request-api="superOrderAbnormalListApi" :init-param="filterData">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-form-item label="用户手机号">
          <el-input v-model="filterData.mobile" type="tel" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="filterData.orderNo" clearable placeholder="请输入" />
        </el-form-item>
      </template>
      <el-table-column type="index" label="序号" width="55"></el-table-column>
      <el-table-column min-width="90" prop="org.cityName" label="城市"></el-table-column>
      <el-table-column min-width="90" prop="type" label="类型" :formatter="({ type }) => typeMap[type]"></el-table-column>
      <el-table-column min-width="300" label="用户信息">
        <template #default="scope">
          <div>{{ scope.row.address.name }} {{ scope.row.address.mobile }}</div>
          <div>{{ (scope.row.address.locationAds || '') + (scope.row.address.detailAds || '') }}</div>
        </template>
      </el-table-column>
      <el-table-column min-width="300" label="服务信息">
        <template #default="scope">
          <div>产品：{{ scope.row.unit }}</div>
          <div>
            陪护时间：{{ $formatDateTime(scope.row.startTime, 'MM月DD日HH:mm') }}~{{
              $formatDateTime(scope.row.endTime, 'MM月DD日HH:mm')
            }}
          </div>
          <div>{{ scope.row.num }}{{ scope.row.unit }}</div>
        </template>
      </el-table-column>
      <el-table-column width="280" label="支付信息">
        <template #default="scope">
          <div>订单编号：{{ scope.row.orderNo }}</div>
          <div>支付金额：{{ scope.row.taAmount }}元</div>
        </template>
      </el-table-column>
      <el-table-column min-width="200" prop="reason" label="异常原因">
        <template #default="scope">
          <div class="red-tip">{{ scope.row.reason }}</div>
        </template>
      </el-table-column>
      <el-table-column
        width="170"
        prop="abnormalTime"
        label="异常时间"
        :formatter="({ abnormalTime }) => $formatDateTime(abnormalTime)"></el-table-column>
      <template v-if="status === 'done'">
        <el-table-column
          fixed="right"
          width="170"
          prop="operationTime"
          label="处理时间"
          :formatter="({ operationTime }) => $formatDateTime(operationTime)"></el-table-column>
        <el-table-column fixed="right" min-width="210" prop="operationPlan" label="处理方案"></el-table-column>
        <el-table-column fixed="right" width="100" prop="operator" label="操作人"></el-table-column>
      </template>
      <el-table-column v-else width="250" label="操作" fixed="right">
        <template #default="scope">
          <el-button type="primary" @click="toOrderInfo(scope.row.orderNo)">详情</el-button>
          <el-button v-if="scope.row.type === 'emer'" type="warning" icon="Edit" @click="toOpen(scope.row)">变更护工</el-button>
          <el-button v-else-if="scope.row.type === 'warn'" type="warning" icon="Bell" @click="toReminderd(scope.row.id)">
            已提醒
          </el-button>
        </template>
      </el-table-column>
    </ProTable>
    <el-dialog v-model="dialogData.visible" :title="dialogData.title" width="500px">
      <el-form :model="dialogData.form" label-position="top" :rules="dialogData.rules" ref="formRef">
        <el-form-item label="用户信息">
          <div>{{ dialogData.form.name }} {{ dialogData.form.mobile }}</div>
          <div>{{ (dialogData.form.locationAds || '') + (dialogData.form.detailAds || '') }}</div>
        </el-form-item>
        <el-form-item label="变更护工" prop="newCarerId" :rules="dialogData.rules.select">
          <el-select
            v-model="dialogData.form.newCarerId"
            filterable
            remote
            remote-show-suffix
            reserve-keyword
            placeholder="请搜索"
            :remote-method="remoteMethod"
            default-first-option
            :loading="dialogData.loading">
            <el-option
              v-for="item in dialogData.carerList"
              :key="item.id"
              :label="`${item.name} ${item.mobile}`"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogData.visible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="abnormalList" pageName="异常订单列表">
import { reactive, ref } from 'vue';
import {
  superOrderAbnormalListApi,
  superOrderSelfCarerListApi,
  superOrderChangeCarerApi,
  superOrderAbnormalConfirmApi
} from '@/api/modules/order.js';
// emer 紧急 warn 警告
const typeMap = {
  emer: '紧急',
  warn: '警告'
};
const props = defineProps({
  status: {
    type: String,
    required: true
  }
});
// 表格搜索数据
const filterData = reactive({
  mobile: '',
  orderNo: '',
  status: props.status
});
// ProTable 实例
const tableRef = ref();

// 弹窗组件功能
const formRef = ref();
const dialogData = reactive({
  visible: false,
  title: '变更护工',
  carerList: [],
  form: {
    id: '',
    newCarerId: '',
    name: '',
    mobile: '',
    locationAds: '',
    detailAds: '',
    cityCode: '',
    location: []
  },
  rules: {
    text: [{ required: true, message: '请输入', trigger: 'blur' }],
    upload: [{ required: true, message: '请上传', trigger: 'change' }],
    select: [{ required: true, message: '请选择', trigger: 'change' }]
  }
});
const superOrderSelfCarerList = async (name) => {
  let res = await superOrderSelfCarerListApi({
    page: 1,
    pageSize: 20,
    cityCode: dialogData.form.cityCode,
    location: dialogData.form.location,
    orderNo: dialogData.form.orderNo,
    name
  });
  dialogData.loading = false;
  if (res && !res.hasErrors) {
    dialogData.carerList = res.data.content;
  } else {
    dialogData.carerList = [];
  }
};
const remoteMethod = (query) => {
  if (query) {
    dialogData.loading = true;
    superOrderSelfCarerListApi(query);
  }
};
const submitForm = async () => {
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;
  let res = await superOrderChangeCarerApi({
    id: dialogData.form.id,
    newCarerId: dialogData.form.newCarerId
  });
  if (res && !res.hasErrors) {
    ElMessage.success('操作成功');
    dialogData.visible = false;
    tableRef.value.getTableList();
  }
};
const toOpen = (row) => {
  dialogData.form = {
    id: row.id,
    name: row.address.name,
    mobile: row.address.mobile,
    locationAds: row.address.locationAds,
    detailAds: row.address.detailAds,
    cityCode: row.org.cityCode,
    location: row.address.location,
    newCarerId: '',
    orderNo: row.orderNo
  };
  superOrderSelfCarerList('');
  dialogData.visible = true;
};

const toOrderInfo = (orderNo) => {
  router.push({
    path: '/orderCenter/orderInfo',
    query: { orderNo }
  });
};

const toReminderd = async (id) => {
  await useHandleData(superOrderAbnormalConfirmApi, { id }, '是否已提醒?');
  tableRef?.value?.getTableList();
};
let timerId = null;
onMounted(() => {
  timerId = setInterval(() => {
    tableRef?.value?.getTableList();
  }, 1000 * 5);
});
onUnmounted(() => {
  clearInterval(timerId);
});
</script>
<style scoped lang="scss">
.label-wrap {
  :deep(.el-form-item__label) {
    line-height: 1.3;
    text-align: right;
  }
}
</style>

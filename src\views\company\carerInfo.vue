<template>
  <el-card>
    <template #header>
      <div class="fyb">
        <div>基本信息</div>
      </div>
    </template>
    <el-form ref="formRef" label-width="140px">
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="所属城市" prop="org.cityName">
            {{ dialogData.form.org.cityName }}
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            {{ dialogData.form.name }}
          </el-form-item>
          <el-form-item label="性别" prop="gender">
            {{ genderMap[dialogData.form.gender] }}
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            {{ dialogData.form.mobile }}
          </el-form-item>
          <el-form-item label="身份证号" prop="idNumber">
            {{ dialogData.form.idNumber }}
          </el-form-item>
          <el-form-item label="年龄" prop="age">
            {{ dialogData.form.age }}
          </el-form-item>
          <!-- 籍贯 -->
          <el-form-item label="籍贯" prop="nativePlace">
            {{ dialogData.form.nativePlace }}
          </el-form-item>
          <!-- 工作年限 -->
          <el-form-item label="工作年限" prop="workYears">
            {{ dialogData.form.workYears }}
            年
          </el-form-item>
          <el-form-item label="等级" prop="grade">
            {{ gradeMap[dialogData.form.grade] }}
          </el-form-item>
          <el-form-item label="资质证书-发证单位" prop="certOrgName">
            {{ dialogData.form.certOrgName }}
          </el-form-item>
          <el-form-item label="资质证书-发证日期" prop="certDate">
            {{ dialogData.form.certDate }}
          </el-form-item>
          <el-form-item label="体检机构" prop="healthInfo.checkOrgName">
            {{ dialogData.form.healthInfo.checkOrgName }}
          </el-form-item>
          <el-form-item label="检测日期" prop="healthInfo.checkDate">
            {{ dialogData.form.healthInfo.checkDate }}
          </el-form-item>
          <!-- 护工备注 -->
          <el-form-item label="护工备注" prop="remark">
            {{ dialogData.form.remark }}
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="个人照" prop="photo">
            <el-image :preview-src-list="[dialogData.form.photo]" :src="dialogData.form.photo" fit="contain" />
          </el-form-item>
          <el-form-item label="身份证照片" prop="idCardImg">
            <el-image
              v-for="(item, index) in dialogData.form.idCardImg"
              :preview-src-list="dialogData.form.idCardImg"
              :initial-index="index"
              :key="item"
              :src="item"
              fit="contain" />
          </el-form-item>
          <el-form-item label="资质证书" prop="certImg">
            <el-image
              v-for="(item, index) in dialogData.form.certImg"
              :preview-src-list="dialogData.form.certImg"
              :initial-index="index"
              :key="item"
              :src="item"
              fit="contain" />
          </el-form-item>
          <el-form-item label="体检报告" prop="healthInfo.healthImg">
            <el-image
              v-for="(item, index) in dialogData.form.healthInfo.healthImg"
              :preview-src-list="dialogData.form.healthInfo.healthImg"
              :initial-index="index"
              :key="item"
              :src="item"
              fit="contain" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="mt40 fc pb40">
      <el-button type="primary" size="large" @click="back">返回</el-button>
    </div>
  </el-card>
</template>

<script setup name="companyCarerDetail" pageName="公司护工详情">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { superCarerDetailApi } from '@/api/modules/carer.js';

const route = useRoute();
const router = useRouter();
const formRef = ref();

const typeTitleMap = {
  XHS: '自主/护工邀请注册',
  XHZ: '小护培训营邀请注册',
  DSF: '护工公司邀请码注册'
};
const genderMap = {
  0: '男',
  1: '女'
};
const dialogData = reactive({
  type: 'info',
  form: {
    age: null,
    certImg: [],
    gender: 1,
    grade: 'JP',
    healthInfo: {
      checkDate: '',
      checkOrgName: '',
      healthImg: []
    },
    idCardImg: [],
    idNumber: '',
    mobile: '',
    name: '',
    nativePlace: '',
    org: {
      cityCode: '',
      cityName: '',
      companyId: '',
      companyName: ''
    },
    photo: '',
    remark: '',
    workYears: null
  }
});
const gradeMap = {
  TP: '铜牌陪护师',
  YP: '银牌陪护师',
  JP: '金牌陪护师'
};
const back = () => {
  router.back();
};
const superCarerDetail = async () => {
  let res = await superCarerDetailApi({
    id: route.query.id
  });
  if (res && !res.hasErrors) {
    Object.assign(dialogData.form, res.data);
  }
};
onMounted(() => {
  superCarerDetail();
});
</script>

<style scoped lang="scss">
:deep(.el-image) {
  width: 150px;
  height: 150px;
  margin-right: 8px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
}
</style>

/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope']
  const ElMessage: typeof import('element-plus/es')['ElMessage']
  const ElMessageBox: typeof import('element-plus/es')['ElMessageBox']
  const checkPhoneNumber: typeof import('../utils/eleValidate')['checkPhoneNumber']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const downLoadFile: typeof import('../utils/index')['downLoadFile']
  const effectScope: typeof import('vue')['effectScope']
  const errorHandler: typeof import('../utils/errorHandler')['default']
  const filterEnum: typeof import('../utils/index')['filterEnum']
  const findItemNested: typeof import('../utils/index')['findItemNested']
  const findMenuByPath: typeof import('../utils/index')['findMenuByPath']
  const formatDateTime: typeof import('../utils/formatter')['formatDateTime']
  const formatTableColumn: typeof import('../utils/index')['formatTableColumn']
  const formatValue: typeof import('../utils/index')['formatValue']
  const genderType: typeof import('../utils/dict')['genderType']
  const generateUUID: typeof import('../utils/index')['generateUUID']
  const getAllBreadcrumbList: typeof import('../utils/index')['getAllBreadcrumbList']
  const getBrowserLang: typeof import('../utils/index')['getBrowserLang']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getDarkColor: typeof import('../utils/color')['getDarkColor']
  const getFlatMenuList: typeof import('../utils/index')['getFlatMenuList']
  const getKeepAliveRouterName: typeof import('../utils/index')['getKeepAliveRouterName']
  const getLightColor: typeof import('../utils/color')['getLightColor']
  const getMenuListPath: typeof import('../utils/index')['getMenuListPath']
  const getShowMenuList: typeof import('../utils/index')['getShowMenuList']
  const getTimeState: typeof import('../utils/index')['getTimeState']
  const getUrlWithParams: typeof import('../utils/index')['getUrlWithParams']
  const h: typeof import('vue')['h']
  const handleProp: typeof import('../utils/index')['handleProp']
  const handleRowAccordingToProp: typeof import('../utils/index')['handleRowAccordingToProp']
  const hexToRgb: typeof import('../utils/color')['hexToRgb']
  const inject: typeof import('vue')['inject']
  const isObjectValueEqual: typeof import('../utils/index')['isObjectValueEqual']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isType: typeof import('../utils/index')['isType']
  const loadingSvg: typeof import('../utils/svg')['loadingSvg']
  const localClear: typeof import('../utils/index')['localClear']
  const localGet: typeof import('../utils/index')['localGet']
  const localRemove: typeof import('../utils/index')['localRemove']
  const localSet: typeof import('../utils/index')['localSet']
  const markRaw: typeof import('vue')['markRaw']
  const mittBus: typeof import('../utils/mittBus')['default']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const provide: typeof import('vue')['provide']
  const randomNum: typeof import('../utils/index')['randomNum']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const regExpPhoneNumber: typeof import('../utils/eleValidate')['regExpPhoneNumber']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const rgbToHex: typeof import('../utils/color')['rgbToHex']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const stores: typeof import('../stores/index')['default']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const useAttrs: typeof import('vue')['useAttrs']
  const useAuthButtons: typeof import('../hooks/useAuthButtons')['useAuthButtons']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useDownload: typeof import('../hooks/useDownload')['useDownload']
  const useHandleData: typeof import('../hooks/useHandleData')['useHandleData']
  const useId: typeof import('vue')['useId']
  const useLink: typeof import('vue-router')['useLink']
  const useModel: typeof import('vue')['useModel']
  const useOnline: typeof import('../hooks/useOnline')['useOnline']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSelection: typeof import('../hooks/useSelection')['useSelection']
  const useSlots: typeof import('vue')['useSlots']
  const useTable: typeof import('../hooks/useTable')['useTable']
  const useTableDom: typeof import('../hooks/useTable')['useTableDom']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useTheme: typeof import('../hooks/useTheme')['useTheme']
  const useTime: typeof import('../hooks/useTime')['useTime']
  const userStatus: typeof import('../utils/dict')['userStatus']
  const usrDateRangeValue: typeof import('../hooks/index.js')['usrDateRangeValue']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
}

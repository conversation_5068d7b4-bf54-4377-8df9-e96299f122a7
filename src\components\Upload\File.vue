<template>
  <div class="upload-box">
    <el-upload
      :id="uuid"
      action="#"
      :class="['upload', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']"
      :multiple="false"
      :disabled="self_disabled"
      :show-file-list="showFileList"
      :http-request="handleHttpUpload"
      :before-upload="beforeUpload"
      :on-success="uploadSuccess"
      :on-error="uploadError"
      :drag="drag"
      :accept="fileType.join(',')">
      <template v-if="modelValue">
        <div class="upload-handle flex justify-between flex-col text-left" @click.stop>
          <div class="fyc">
            <div class="t-hide lh-[1] w-0 flex-1">{{ fileName }}</div>
            <el-icon class="text-primary!"><SuccessFilled /></el-icon>
          </div>
          <div class="fyc mt-auto">
            <el-button :disabled="self_disabled" text type="primary" @click="editFile">重新上传</el-button>
            <el-button :disabled="self_disabled" text type="danger" @click="deleteFile">删除</el-button>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="upload-empty">
          <slot name="empty">
            <el-icon><UploadFilled /></el-icon>
            <span>
              <span class="text-primary">点击上传</span>
              /拖拽到此区域
            </span>
          </slot>
        </div>
      </template>
    </el-upload>
    <div class="el-upload__tip">
      <slot name="tip"></slot>
    </div>
  </div>
</template>

<script setup lang="ts" name="UploadImg">
import { ref, computed, inject } from 'vue';
import { generateUUID } from '@/utils';
import { uploadImg } from '@/api/modules/upload';
import { ElNotification, formContextKey, formItemContextKey } from 'element-plus';
import type { UploadProps, UploadRequestOptions } from 'element-plus';
const modelValue = defineModel({
  type: String,
  default: ''
});
interface UploadFileProps {
  api?: (params: any) => Promise<any>; // 上传文件的 api 方法，一般项目上传都是同一个 api 方法，在组件里直接引入即可 ==> 非必传
  drag?: boolean; // 是否支持拖拽上传 ==> 非必传（默认为 true）
  showFileList?: boolean; // 是否显示文件列表 ==> 非必传（默认为 false）
  disabled?: boolean; // 是否禁用上传组件 ==> 非必传（默认为 false）
  fileSize?: number; // 文件大小限制 ==> 非必传（默认为 5M）
  fileType?: File.ExcelMimeType[]; // 文件类型限制 ==> 非必传（默认为 ["image/jpeg", "image/png", "image/gif"]）
  height?: string; // 组件高度 ==> 非必传（默认为 150px）
  width?: string; // 组件宽度 ==> 非必传（默认为 150px）
  borderRadius?: string; // 组件边框圆角 ==> 非必传（默认为 8px）
}
const fileName = ref('');
// 接受父组件参数
const props = withDefaults(defineProps<UploadFileProps>(), {
  drag: true,
  showFileList: false,
  disabled: false,
  fileSize: 30,
  fileType: () => ['csv', 'text/csv', 'xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  height: '120px',
  width: '300px',
  borderRadius: '8px'
});

// 生成组件唯一id
const uuid = ref('id-' + generateUUID());

// 获取 el-form 组件上下文
const formContext = inject(formContextKey, void 0);
// 获取 el-form-item 组件上下文
const formItemContext = inject(formItemContextKey, void 0);
// 判断是否禁用上传和删除
const self_disabled = computed(() => {
  return props.disabled || formContext?.disabled;
});
const emit = defineEmits(['uploadSuccess']);
/**
 * @description 文件上传
 * @param options upload 所有配置项
 * */
const handleHttpUpload = async (options: UploadRequestOptions) => {
  let formData = new FormData();
  formData.append('file', options.file);
  try {
    const api = props.api ?? uploadImg;
    const { data } = await api(formData);
    emit('uploadSuccess', data);
    fileName.value = options.file.name;
    modelValue.value = data;
    // 调用 el-form 内部的校验方法（可自动校验）
    formItemContext?.prop && formContext?.validateField([formItemContext.prop as string]);
  } catch (error) {
    options.onError(error as any);
  }
};

/**
 * @description 删除文件
 * */
const deleteFile = () => {
  fileName.value = '';
  modelValue.value = '';
};

/**
 * @description 编辑文件
 * */
const editFile = () => {
  const dom = document.querySelector(`#${uuid.value} .el-upload__input`);
  dom && dom.dispatchEvent(new MouseEvent('click'));
};

/**
 * @description 文件上传之前判断
 * @param rawFile 选择的文件
 * */
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const fileSize = rawFile.size / 1024 / 1024 < props.fileSize;
  console.log('rawFile.type：', rawFile.type);
  const fileType = props.fileType.includes(rawFile.type as File.ExcelMimeType);
  if (!fileType)
    ElNotification({
      title: '温馨提示',
      message: '上传文件不符合所需的格式！',
      type: 'warning'
    });
  if (!fileSize)
    setTimeout(() => {
      ElNotification({
        title: '温馨提示',
        message: `上传文件大小不能超过 ${props.fileSize}M！`,
        type: 'warning'
      });
    }, 0);
  return fileType && fileSize;
};

/**
 * @description 文件上传成功
 * */
const uploadSuccess = () => {
  ElNotification({
    title: '温馨提示',
    message: '文件上传成功！',
    type: 'success'
  });
};

/**
 * @description 文件上传错误
 * */
const uploadError = () => {
  ElNotification({
    title: '温馨提示',
    message: '文件上传失败，请您重新上传！',
    type: 'error'
  });
};
</script>

<style scoped lang="scss">
.is-error {
  .upload {
    :deep(.el-upload),
    :deep(.el-upload-dragger) {
      border: 1px dashed var(--el-color-danger) !important;
      &:hover {
        border-color: var(--el-color-primary) !important;
      }
    }
  }
}
:deep(.disabled) {
  .el-upload,
  .el-upload-dragger {
    cursor: not-allowed !important;
    background: var(--el-disabled-bg-color);
    border: 1px dashed var(--el-border-color-darker) !important;
    &:hover {
      border: 1px dashed var(--el-border-color-darker) !important;
    }
  }
}
.upload-box {
  .no-border {
    :deep(.el-upload) {
      border: none !important;
    }
  }
  :deep(.upload) {
    .el-upload {
      position: relative;
      width: v-bind(width);
      height: v-bind(height);
      overflow: hidden;
      border: 1px dashed var(--el-border-color-darker);
      border-radius: v-bind(borderRadius);
      transition: var(--el-transition-duration-fast);
      .el-upload-dragger {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 0;
        overflow: hidden;
        background-color: transparent;
        border: 1px dashed var(--el-border-color-darker);
        border-radius: v-bind(borderRadius);
        &:hover {
          border: 1px dashed var(--el-color-primary);
        }
      }
      .el-upload-dragger.is-dragover {
        background-color: var(--el-color-primary-light-9);
        border: 2px dashed var(--el-color-primary) !important;
      }
      .upload-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      .upload-empty {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        line-height: 30px;
        color: var(--el-color-info);
        .el-icon {
          font-size: 28px;
          color: var(--el-text-color-secondary);
        }
      }
      .upload-handle {
        position: absolute;
        top: 0;
        right: 0;
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        padding: 10px;
      }
    }
  }
  .el-upload__tip {
    line-height: 18px;
    text-align: center;
  }
}
</style>

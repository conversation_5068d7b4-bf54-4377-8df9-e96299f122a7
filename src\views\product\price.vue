<template>
  <div>
    <el-card>
      <template #header>
        <div class="fyc">
          请选择城市：
          <el-select @change="getFromData" class="w200!" v-model="submitData.form.cityCode" filterable placeholder="请选择">
            <el-option
              v-for="item in submitData.cityList"
              :key="item.cityCode"
              :label="item.cityName"
              :value="item.cityCode"></el-option>
          </el-select>
        </div>
      </template>
      <el-form :model="submitData.form" label-width="110px">
        <el-row class="my50" justify="center" :gutter="10" v-for="(item, index) in arrKey" :key="item.value">
          <el-col :span="2">{{ item.label }}</el-col>
          <el-col :span="5" v-for="(priceItem, priceIndex) in submitData.form[item.value]" :index="priceItem.productType">
            <el-form-item :prop="`${item.value}.${priceIndex}.price`" :rules="rules.text" :label="typeMap[priceItem.productType]">
              <el-input-number class="mr10" v-model="submitData.form[item.value][priceIndex].price"></el-input-number>
              元
            </el-form-item>
          </el-col>
        </el-row>
        <div class="fcc">
          <el-button type="info" @click="getFromData">取消</el-button>
          <el-button type="primary" @click="saveFormData">保存</el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>
<script setup name="Basic">
import { reactive, ref } from 'vue';
import { superProductPriceQueryApi, superProductPriceSaveApi } from '@/api/modules/product.js';
import { areaListApi } from '@/api/modules/system.js';
import { cloneDeep } from 'lodash-es';

const typeMap = {
  QT: '全天（24H）',
  RP: '日陪（12H）',
  YP: '夜陪（12H）'
};
const arrKey = [
  {
    label: '完全自理',
    value: 'itemsA'
  },
  {
    label: '部分自理',
    value: 'itemsB'
  },
  {
    label: '不可自理',
    value: 'itemsC'
  }
];
const defaultData = {
  cityCode: '',
  itemsA: [
    {
      price: 0,
      productType: 'QT'
    },
    {
      price: 0,
      productType: 'RP'
    },
    {
      price: 0,
      productType: 'YP'
    }
  ],
  itemsB: [
    {
      price: 0,
      productType: 'QT'
    },
    {
      price: 0,
      productType: 'RP'
    },
    {
      price: 0,
      productType: 'YP'
    }
  ],
  itemsC: [
    {
      price: 0,
      productType: 'QT'
    },
    {
      price: 0,
      productType: 'RP'
    },
    {
      price: 0,
      productType: 'YP'
    }
  ]
};
const submitData = reactive({
  cityList: [],
  form: cloneDeep(defaultData)
});
const rules = {
  text: [{ required: true, message: '请输入', trigger: 'blur' }]
};

const submitForm = async () => {
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;
  let res = await tenantContactAddApi(dialogData.form);
  if (res && !res.hasErrors) {
    ElMessage.success('提交成功');
    dialogData.visible = false;
    tableRef.value.getTableList();
  }
};

const getFromData = async () => {
  let res = await superProductPriceQueryApi({
    cityCode: submitData.form.cityCode
  });
  submitData.form = res.data || {
    ...defaultData,
    cityCode: submitData.form.cityCode
  };
};

const saveFormData = async () => {
  let res = await superProductPriceSaveApi(submitData.form);
  if (res && !res.hasErrors) {
    ElMessage.success('保存成功');
  }
};
async function areaList() {
  let res = await areaListApi({ level: 0 });
  if (res && !res.hasErrors) {
    submitData.cityList = res.data.map((d) => ({
      cityCode: d.code,
      cityName: d.name
    }));
    submitData.form.cityCode = submitData.cityList[0].cityCode;
    getFromData();
  }
}
areaList();
// getFromData('maxLimit', 'form1');
</script>
<style scoped lang="scss">
.el-form-item {
  margin-bottom: 0;
}
.text-hign {
  color: #fb8240;
}
</style>

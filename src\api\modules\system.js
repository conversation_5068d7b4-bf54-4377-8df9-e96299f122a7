import http from '@/api';

/**
 * @name 系统相关API
 */
// 系统资源列表
export const resourceListApi = (data) => http.post('/super/admin/sys/menus/list', data);
// 商家-系统默认菜单-返回全部的菜单
export const superSysMenusLazyApi = (params) => http.get('/super/admin/sys/menus/lazy', params);
// 根据菜单ID返回所有子节点ID，包含自身ID
export const superSysMenusChildApi = (params) => http.get('/super/admin/sys/menus/child', params);
// 修改角色菜单
export const superSysRoleMenuApi = (data) => http.post('/super/admin/sys/role/menu', data);
// 商家-系统默认菜单-新增
export const superSysMenusCreateApi = (data) => http.post('/super/admin/sys/menus/create', data);
// 商家-系统默认菜单-删除
export const superSysMenusDeleteApi = (params) => http.delete('/super/admin/sys/menus/delete', params);
// 商家-系统默认菜单-修改
export const superSysMenusUpdateApi = (data) => http.post('/super/admin/sys/menus/update', data);
// 系统角色新增
export const roleCreateApi = (data) => http.post('/super/admin/sys/role/create', data);
// 系统角色删除
export const roleDeleteApi = (params) => http.delete('/super/admin/sys/role/delete', params);
// 系统角色列表
export const roleListApi = (data) => http.post('/super/admin/sys/role/list', data);
// 系统角色详情
export const roleQueryApi = (params) => http.get('/super/admin/sys/role/query', params);
// 系统角色修改
export const roleUpdateApi = (data) => http.post('/super/admin/sys/role/update', data);
// 系统用户新增
export const userCreateApi = (data) => http.post('/super/admin/sys/user/create', data);
// 系统用户列表
export const userListApi = (data) => http.post('/super/admin/sys/user/list', data);
// 系统用户详情
export const userQueryApi = (params) => http.get('/super/admin/sys/user/query', params);
// 系统用户修改
export const userUpdateApi = (data) => http.post('/super/admin/sys/user/update', data);
// 系统用户修改状态
export const userUpdateStatusApi = (data) => http.post('/super/admin/sys/user/update/status', data);
// 行政地区信息
export const areaListApi = (data) => http.post('/public/area/list', data);

// 技能标签列表
export const superSkillListApi = (data) => {
  return http.post('/super/admin/skill/list', data);
};

// 技能标签-新增
export const superSkillCreateApi = (data) => {
  return http.post('/super/admin/skill/create', data);
};

// 技能标签-删除
export const superSkillDeleteApi = (params) => {
  return http.delete('/super/admin/skill/delete', params);
};

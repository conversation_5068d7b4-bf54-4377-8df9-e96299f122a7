<template>
  <div class="icon-body">
    <el-input
      v-model="name"
      style="position: relative"
      clearable
      placeholder="请输入图标名称"
      @clear="filterIcons"
      @input="filterIcons">
      <template #suffix>
        <el-icon><Search /></el-icon>
      </template>
    </el-input>
    <div class="icon-list">
      <div v-for="(item, index) in iconList" :key="index" @click="selectedIcon(item.path)">
        <SvgIcon :name="item.path" :iconStyle="{ height: '30px', width: '16px', fill: 'currentColor' }" />
        <span>{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import icons from './requireIcons';
import SvgIcon from '@/components/SvgIcon/index.vue';
import { Search } from '@element-plus/icons-vue';

const name = ref('');
const iconList = ref([...icons]);

const filterIcons = (value) => {
  console.log('value: ', value, icons);
  if (value) {
    iconList.value = icons.filter((item) => item.name.includes(value));
  } else {
    iconList.value = [...icons];
  }
};

const emit = defineEmits(['selected']);
const selectedIcon = (iconName) => {
  emit('selected', iconName);
  document.body.click();
};

const reset = () => {
  name.value = '';
  iconList.value = [...icons];
};

// 导出 reset
defineExpose({
  reset
});
</script>

<style scoped lang="scss">
.icon-body {
  width: 100%;
  .icon-list {
    height: 200px;
    margin-top: 10px;
    overflow-y: scroll;
    div {
      float: left;
      display: flex;
      align-items: center;
      width: 33%;
      height: 30px;
      margin-bottom: -5px;
      line-height: 30px;
      cursor: pointer;
    }
    span {
      display: inline-block;
      margin-left: 5px;
      overflow: hidden;
      vertical-align: -0.15em;
      fill: currentColor;
    }
  }
}
</style>

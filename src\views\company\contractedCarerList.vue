<template>
  <div class="table-box">
    <!--表格渲染-->
    <ProTable ref="tableRef" :requestApi="companyCarerListApi" :init-param="filterData">
      <template #tableHeader>
        <el-form inline>
          <el-form-item label="姓名:">
            <el-input clearable v-model="filterData.name"></el-input>
          </el-form-item>
          <el-form-item label="手机号:">
            <el-input clearable v-model="filterData.mobile"></el-input>
          </el-form-item>
          <el-form-item label="等级" prop="type">
            <el-select v-model="filterData.grade" placeholder="请选择" clearable>
              <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <br />
        </el-form>
      </template>
      <template #tableHeaderButton>
        <el-button type="primary" :icon="Plus" @click="openDialog">邀请护工入驻平台</el-button>
      </template>
      <el-table-column type="index" label="序号" width="55"></el-table-column>
      <el-table-column width="90" prop="name" label="姓名"></el-table-column>
      <el-table-column width="130" prop="mobile" label="手机号"></el-table-column>
      <el-table-column width="80" prop="gender" label="性别" :formatter="(row) => sexMap[row.gender] || ''"></el-table-column>
      <el-table-column min-width="270" prop="profile" label="简介信息"></el-table-column>
      <el-table-column width="90" prop="type" label="类型" :formatter="(row) => typeMap[row.type] || ''"></el-table-column>
      <el-table-column width="120" prop="grade" label="等级" :formatter="(row) => gradeMap[row.grade] || ''"></el-table-column>
      <el-table-column width="90" prop="org.cityName" label="所属城市"></el-table-column>
      <el-table-column min-width="150" prop="org.companyName" label="所属护工公司"></el-table-column>
      <el-table-column width="110" prop="orderNum" label="已服务单数"></el-table-column>
      <el-table-column width="120" prop="status" label="状态">
        <template #default="scope">
          <div :class="statusColorMap[scope.row.status]">{{ statusMap[scope.row.status] }}</div>
        </template>
      </el-table-column>
      <el-table-column
        width="90"
        prop="userStatus"
        label="登录账号"
        :formatter="(row) => userStatusMap[row.userStatus] || ''"></el-table-column>
      <el-table-column
        width="170"
        prop="createdTime"
        label="创建时间"
        :formatter="({ createdTime }) => $formatDateTime(createdTime)"></el-table-column>
      <el-table-column label="操作" width="110px" align="center" fixed="right">
        <template #default="scope">
          <el-button type="primary" @click="toCarerInfo(scope.row.id)">详情</el-button>
        </template>
      </el-table-column>
    </ProTable>
    <el-dialog center v-model="dialogData.visible" :title="dialogData.title" width="500px">
      <img :src="dialogData.imgUrl" class="block w300 mx-auto" />
    </el-dialog>
  </div>
</template>

<script setup name="contractedCarerList" pageName="公司签约护工">
import { ref, reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { companyCarerListApi, companyExtendCodeApi } from '@/api/modules/company.js';

import { Plus, Delete, Edit } from '@element-plus/icons-vue';
import router from '@/routers';
import { downLoadFile } from '@/utils';
const filterData = reactive({
  name: '',
  mobile: '',
  type: '',
  grade: '',
  companyId: ''
});
const statusColorMap = {
  study: 'text-#fa2400',
  free: 'text-#0db817',
  run: 'text-#ff7f50',
  stop: 'text-#fa2400'
};
const statusMap = {
  study: '待培训',
  free: '接单中',
  run: '服务中',
  stop: '已暂停'
};
const userStatusMap = {
  normal: '正常',
  disabled: '已暂停'
};
const sexMap = {
  0: '男',
  1: '女'
};

const typeOptions = [
  {
    label: '平台签约',
    value: 'XHS'
  },
  {
    label: '平台自营',
    value: 'XHZ'
  },
  {
    label: '第三方',
    value: 'DSF'
  }
];
const typeMap = {
  XHS: '平台签约',
  XHZ: '平台自营',
  DSF: '第三方'
};
const gradeOptions = [
  {
    label: '铜牌陪护师',
    value: 'TP'
  },
  {
    label: '银牌陪护师',
    value: 'YP'
  },
  {
    label: '金牌陪护师',
    value: 'JP'
  }
];
const gradeMap = {
  TP: '铜牌陪护师',
  YP: '银牌陪护师',
  JP: '金牌陪护师'
};
const { tableRef, refreshDataList } = useTableDom('tableRef');
const toCarerInfo = (id) => {
  router.push({ path: '/company/carerInfo', query: { id } });
};

const dialogData = reactive({
  visible: false,
  title: '截图或下载小程序码，邀请公司签约护工入驻平台吧！',
  imgUrl: ''
});

const companyExtendCode = async () => {
  let res = await companyExtendCodeApi();
  if (res && !res.hasErrors) {
    dialogData.imgUrl = res.data;
    dialogData.visible = true;
  }
};
const openDialog = () => {
  if (dialogData.imgUrl) {
    dialogData.visible = true;
  } else {
    companyExtendCode();
  }
};
</script>

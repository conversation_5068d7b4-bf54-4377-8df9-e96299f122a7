<template>
  <div class="table-box">
    <ProTable ref="tableRef" :requestApi="superSkillListApi" :init-param="filterData">
      <template #tableHeader>
        <el-form inline @submit.prevent>
          <el-form-item label="名称:">
            <el-input clearable v-model="filterData.name"></el-input>
          </el-form-item>
        </el-form>
      </template>
      <template #tableHeaderButton>
        <el-button type="primary" :icon="Plus" @click="createUser">新增</el-button>
      </template>
      <el-table-column type="index" label="序号" width="55"></el-table-column>
      <el-table-column prop="name" label="名称"></el-table-column>
      <el-table-column label="创建时间" :formatter="(row) => $formatDateTime(row.createdTime)"></el-table-column>
      <el-table-column prop="operator" label="创建人"></el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="danger" @click="toDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </ProTable>
    <!-- 新建/编辑 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" center width="400px">
      <el-form @submit.prevent ref="dialogFormVue" label-width="80px" :model="dialog.form" :rules="dialog.rules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="dialog.form.name" clearable></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialog.visible = false">取消</el-button>
        <el-button @click="save" type="primary">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="skillTag">
import { ref, reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { superSkillListApi, superSkillDeleteApi, superSkillCreateApi } from '@/api/modules/system.js';
import { Plus } from '@element-plus/icons-vue';

const statusMap = {
  normal: '正常',
  disabled: '停用'
};
const filterData = reactive({ roleId: '' });
const dialog = reactive({
  title: '',
  visible: false,
  form: {
    id: '',
    name: '',
    mobile: '',
    roleId: '',
    roleName: ''
  },
  rules: {
    name: [{ required: true, trigger: 'blur', message: '请输入姓名' }],
    mobile: [{ required: true, trigger: 'blur', message: '请输入手机号' }],
    roleId: [{ required: true, trigger: 'blur', message: '请选择角色' }]
  }
});
const { tableRef, refreshDataList } = useTableDom('tableRef');

const dialogFormVue = ref();

async function save() {
  const valid = await dialogFormVue.value.validate().catch(() => false);
  if (!valid) return;
  let res = await superSkillCreateApi(dialog.form);
  if (res && !res.hasErrors) {
    ElMessage.success('操作成功');
    dialog.visible = false;
    refreshDataList();
  }
}

function createUser() {
  dialog.form = {
    name: ''
  };
  dialog.title = '新增技能标签';
  dialog.visible = true;
  setTimeout(() => {
    dialogFormVue.value && dialogFormVue.value.clearValidate();
  });
}
const toDelete = async (id) => {
  await useHandleData(superSkillDeleteApi, { id }, '是否删除?');
  refreshDataList();
};
</script>

<style lang="scss" scoped></style>

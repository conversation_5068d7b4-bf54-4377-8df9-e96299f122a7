import http from '@/api';
import authMenuList from '@/assets/json/authMenuList.json';
import authButtonList from '@/assets/json/authButtonList.json';
import { Login } from '@/api/interface/index';

/**
 * @name 登录模块
 */
// 获取验证码
export const securityCodeApi = (params) => {
  return http.post('/public/admin/security/code', params);
};

// 用户登录
export const loginApi = (params: Login.ReqLoginForm) => {
  return http.post<Login.ResLogin>('/public/super/admin/login', params, { loading: false });
};

// 获取菜单列表
export const getAuthMenuListApi = () => {
  // return authMenuList;
  return http.get<Menu.MenuOptions[]>('/super/admin/user/resource');
};

// 获取按钮权限
export const getAuthButtonListApi = () => {
  return authButtonList;
};

// 用户退出登录
export const logoutApi = () => {
  return http.post('/logout');
};

import http from '@/api';

// 黑名单列表
export const tenantBlacklistPageApi = (data) => {
  return http.post('/tenant-blacklist/page', data);
};

// 新增黑名单
export const tenantBlacklistAddApi = (data) => {
  return http.post('/tenant-blacklist/add', data);
};

// 删除黑名单
export const tenantBlacklistDeleteApi = (data) => {
  return http.post('/tenant-blacklist/delete', data);
};

// 告警联系人列表
export const tenantContactPageApi = (data) => {
  return http.post('/tenant-contact/page', data);
};

// 新增告警联系人
export const tenantContactAddApi = (data) => {
  return http.post('/tenant-contact/add', data);
};

// 删除告警联系人
export const tenantContactDeleteApi = (data) => {
  return http.post('/tenant-contact/delete', data);
};

// 查询短信状态回调设置
export const tenantBaseCallbackUrlQueryApi = (params) => {
  return http.get('/tenant-base/callback/url/query', {}, { params });
};

// 短信状态回调设置-保存
export const tenantBaseCallbackUrlSaveApi = (data) => {
  return http.post('/tenant-base/callback/url/save', data);
};

// 查询发送频率限制设置
export const tenantBaseFrequencyLimitQueryApi = (params) => {
  return http.get('/tenant-base/frequency/limit/query', {}, { params });
};

// 发送频率限制设置-保存
export const tenantBaseFrequencyLimitSaveApi = (data) => {
  return http.post('/tenant-base/frequency/limit/save', data);
};

// 查询-国内短信阈值设置
export const tenantBaseMaxLimitQueryApi = (params) => {
  return http.get('/tenant-base/max/limit/query', {}, { params });
};

// 国内短信阈值设置-保存
export const tenantBaseMaxLimitSaveApi = (data) => {
  return http.post('/tenant-base/max/limit/save', data);
};

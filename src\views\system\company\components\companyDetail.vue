<template>
  <el-dialog v-model="dialogData.visible" :title="dialogData.title" width="700px" v-bind="$attrs">
    <el-form ref="formRef" label-width="140px" :model="dialogData.form" :rules="dialogData.rules">
      <el-form-item label="所属城市" prop="cityCode">
        <el-select @change="cityChange" v-model="selectCity" filterable placeholder="请选择" value-key="cityCode">
          <el-option v-for="item in cityList" :key="item.cityCode" :label="item.cityName" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-input v-model="dialogData.form.companyName"></el-input>
      </el-form-item>
      <el-form-item label="联系人姓名" prop="contactName">
        <el-input v-model="dialogData.form.contactName"></el-input>
      </el-form-item>
      <el-form-item label="联系人手机号" prop="contactMobile">
        <el-input v-model="dialogData.form.contactMobile"></el-input>
      </el-form-item>
      <el-form-item label="签约医院" prop="hospitals">
        <el-select
          v-model="dialogData.form.hospitals"
          multiple
          filterable
          remote
          remote-show-suffix
          reserve-keyword
          placeholder="请搜索"
          :remote-method="remoteMethod"
          :loading="dialogData.loading">
          <el-option v-for="item in dialogData.hospitalList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogData.visible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup name="companyDetail" pageName="商户详情">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { superCompanyCreateApi, superCompanyUpdateApi, superHospitalSelectListApi } from '@/api/modules/systemCompany.js';
import tags from '@/components/Tags/index.vue';
import { areaListApi } from '@/api/modules/system.js';

const formRef = ref();
const selectCity = ref({ cityCode: '', cityName: '' });
const cityList = ref([]);

const dialogData = reactive({
  visible: false,
  title: '公司信息',
  type: 'add',
  loading: false,
  hospitalList: [],
  form: {
    cityCode: '',
    cityName: '',
    companyName: '',
    contactMobile: '',
    contactName: '',
    hospitals: []
  },
  rules: {
    companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
    contactMobile: [
      { required: true, message: '请输入联系人手机号', trigger: 'blur' },
      { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
    ],
    contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
    hospitals: [
      { required: true, message: '请输入医院名称', trigger: 'blur' },
      { type: 'array', required: true, message: '请至少输入一个医院', trigger: 'blur' }
    ],
    cityCode: [{ required: true, message: '请选择所属城市', trigger: 'change' }]
  }
});

const emit = defineEmits(['saved']);

async function submitForm() {
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;
  let res = null;
  if (dialogData.type === 'update') {
    res = await superCompanyUpdateApi(dialogData.form);
  } else {
    res = await superCompanyCreateApi(dialogData.form);
  }
  if (res && !res.hasErrors) {
    ElMessage.success('操作成功');
    dialogData.visible = false;
    emit('saved');
  }
}

async function areaList() {
  let res = await areaListApi({ level: 0 });
  if (res && !res.hasErrors) {
    cityList.value = res.data.map((d) => ({
      cityCode: d.code,
      cityName: d.name
    }));
  }
}

function cityChange(val) {
  dialogData.form.cityCode = val.cityCode;
  dialogData.form.cityName = val.cityName;
}

const toAdd = () => {
  dialogData.type = 'add';
  dialogData.title = '公司信息';
  dialogData.form = {
    cityCode: '',
    cityName: '',
    companyName: '',
    contactMobile: '',
    contactName: '',
    hospitals: []
  };
  selectCity.value = { cityCode: '', cityName: '' };
  nextTick(() => {
    formRef.value && formRef.value.clearValidate();
  });
  dialogData.visible = true;
};

const toEdit = (row) => {
  dialogData.type = 'update';
  dialogData.title = '编辑公司信息';
  dialogData.form = { ...row };
  selectCity.value = { cityCode: row.cityCode, cityName: row.cityName };
  nextTick(() => {
    formRef.value && formRef.value.clearValidate();
  });
  dialogData.visible = true;
};
const superHospitalSelectList = async (name) => {
  let res = await superHospitalSelectListApi({
    page: 1,
    pageSize: 10,
    name
  });
  dialogData.loading = false;
  if (res && !res.hasErrors) {
    dialogData.hospitalList = res.data.content;
  }
};
const remoteMethod = (query) => {
  if (query) {
    dialogData.loading = true;
    superHospitalSelectList(query);
  }
};

defineExpose({
  dialogData,
  submitForm,
  toAdd,
  toEdit
});

onMounted(() => {
  areaList();
  superHospitalSelectList('');
});
</script>

<style scoped lang="scss"></style>

<template>
  <div class="out-page">
    <ProTable ref="tableRef" :requestApi="userListApi" :init-param="filterData">
      <template #tableHeader>
        <el-form inline @submit.prevent>
          <el-form-item label="姓名:">
            <el-input clearable v-model="filterData.name"></el-input>
          </el-form-item>
        </el-form>
      </template>
      <template #tableHeaderButton>
        <el-button type="primary" :icon="Plus" @click="createUser">添加账号</el-button>
      </template>
      <el-table-column type="index" label="序号" width="55"></el-table-column>
      <el-table-column width="250px" label="账号信息">
        <template #default="scope">
          <div>
            <div>姓名：{{ scope.row.name }}</div>
            <div>手机号：{{ scope.row.mobile }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="roleName" label="角色"></el-table-column>
      <el-table-column label="创建时间" :formatter="(row) => $formatDateTime(row.createdTime)"></el-table-column>
      <el-table-column label="最后登录时间" :formatter="(row) => $formatDateTime(row.lastLoginTime)"></el-table-column>
      <el-table-column label="状态">
        <template #default="scope">
          {{ statusMap[scope.row.status] }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="primary" @click="updateInfo(scope.row)">编辑</el-button>
          <el-button
            v-if="scope.row.status !== 'leave'"
            :type="scope.row.status === 'normal' ? 'danger' : 'primary'"
            @click="updateStatus(scope.row)">
            {{ scope.row.status === 'normal' ? '停用' : '启用' }}
          </el-button>
        </template>
      </el-table-column>
    </ProTable>
    <!-- 新建/编辑 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" center width="800px">
      <el-form ref="dialogFormVue" label-width="140px" :model="dialog.form" :rules="dialog.rules">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="dialog.form.name" clearable></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="dialog.form.mobile" clearable></el-input>
        </el-form-item>
        <el-form-item label="选择角色" prop="roleId">
          <el-select v-model="dialog.form.roleId" clearable @change="roleChange">
            <el-option v-for="(item, index) in roleList" :key="index" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialog.visible = false">取消</el-button>
        <el-button @click="save" type="primary">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="accountList">
import { ref, reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { userListApi, userUpdateApi, roleListApi, userCreateApi, userUpdateStatusApi } from '@/api/modules/system.js';
import { Plus } from '@element-plus/icons-vue';

const statusMap = {
  normal: '正常',
  disabled: '停用'
};
const filterData = reactive({ roleId: '' });
const roleList = ref([]);
const dialog = reactive({
  title: '',
  visible: false,
  form: {
    id: '',
    name: '',
    mobile: '',
    roleId: '',
    roleName: ''
  },
  rules: {
    name: [{ required: true, trigger: 'blur', message: '请输入姓名' }],
    mobile: [{ required: true, trigger: 'blur', message: '请输入手机号' }],
    roleId: [{ required: true, trigger: 'blur', message: '请选择角色' }]
  }
});
const { tableRef, refreshDataList } = useTableDom('tableRef');

const dialogFormVue = ref();

async function getRoleList() {
  let res = await roleListApi({ page: 1, pageSize: 999 });
  if (res && !res.hasErrors) {
    roleList.value = res.data.content;
  }
}

function roleChange(roleId) {
  let index = roleList.value.findIndex((v) => v.id === roleId);
  if (index > -1) {
    dialog.form.roleName = roleList.value[index]['name'];
  }
}

async function save() {
  const valid = await dialogFormVue.value.validate().catch(() => false);
  if (!valid) return;
  let res;
  if (dialog.form.id) {
    res = await userUpdateApi(dialog.form);
  } else {
    res = await userCreateApi(dialog.form);
  }
  if (res && !res.hasErrors) {
    ElMessage.success('操作成功');
    dialog.visible = false;
    refreshDataList();
  }
}

function updateInfo(row) {
  dialog.form = { ...row };
  dialog.title = '编辑';
  dialog.visible = true;
  setTimeout(() => {
    dialogFormVue.value && dialogFormVue.value.clearValidate();
  });
}

function createUser() {
  dialog.form = {
    name: '',
    mobile: '',
    roleId: '',
    roleName: ''
  };
  dialog.title = '添加账号';
  dialog.visible = true;
  setTimeout(() => {
    dialogFormVue.value && dialogFormVue.value.clearValidate();
  });
}

function updateStatus(row) {
  ElMessageBox.confirm(`确定${row.status === 'normal' ? '停用' : '启用'}该用户吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const res = await userUpdateStatusApi({
      id: row.id,
      status: row.status === 'normal' ? 'disabled' : 'normal'
    });
    if (res && !res.hasErrors) {
      refreshDataList();
      ElMessage.success('操作成功');
    }
  });
}

onMounted(() => {
  getRoleList();
});
</script>

<style lang="scss" scoped></style>

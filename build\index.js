/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/04 22:48:02
 * @Email: <EMAIL>
 * Copyright © 2023 Ronnie <PERSON>(大脸怪) | https://isme.top
 **********************************/

import path from 'node:path';
import { globSync } from 'glob';

/**
 * @usage 生成icons, 用于 unocss safelist，以支持页面动态渲染自定义图标
 */
export function getIcons() {
  const icons = {};
  const files = globSync('src/assets/icons/**/*.svg', { nodir: true, strict: true });
  files.forEach((filePath) => {
    const fileName = path.basename(filePath); // 获取文件名，包括后缀
    const fileNameWithoutExt = path.parse(fileName).name; // 获取去除后缀的文件名
    const folderName = path.basename(path.dirname(filePath)); // 获取文件夹名
    if (!icons[folderName]) {
      icons[folderName] = [];
    }
    icons[folderName].push(`i-${folderName}:${fileNameWithoutExt}`);
    // icons[folderName].push(fileNameWithoutExt);
  });
  return icons;
}

/**
 * @usage 生成.vue文件路径列表，用于添加菜单时可下拉选择对应的.vue文件路径，防止手动输入报错
 */
export function getPagePathes() {
  const files = globSync('src/views/**/*.vue');
  return files.map((item) => `/${path.normalize(item).replace(/\\/g, '/')}`);
}

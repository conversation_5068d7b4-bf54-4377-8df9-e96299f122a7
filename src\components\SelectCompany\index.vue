<template>
  <el-select
    v-model="modelValue"
    clearable
    filterable
    remote
    remote-show-suffix
    reserve-keyword
    placeholder="请搜索"
    :remote-method="remoteMethod"
    :loading="state.loading"
    v-bind="$attrs">
    <el-option v-for="item in state.companyList" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>
<script setup name="SelectCompany">
import { superCompanyListApi } from '@/api/modules/systemCompany.js';

const modelValue = defineModel({
  type: String,
  default: ''
});

const state = reactive({
  loading: false,
  companyList: []
});
const superCompanyList = async (companyName) => {
  let res = await superCompanyListApi({
    page: 1,
    pageSize: 10,
    companyName
  });
  state.loading = false;
  if (res && !res.hasErrors) {
    state.companyList = res.data.content.map((d) => {
      return {
        label: d.companyName,
        value: d.id
      };
    });
  }
};
const remoteMethod = (query) => {
  if (query) {
    state.loading = true;
    superCompanyList(query);
  }
};
onMounted(() => {
  superCompanyList('');
});
</script>
<style scoped lang="scss"></style>

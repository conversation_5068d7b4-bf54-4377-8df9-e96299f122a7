<template>
  <div class="table-box">
    <el-row class="flex-1" :gutter="15">
      <!--角色管理-->
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="17" style="margin-bottom: 10px">
        <el-card class="box-card" shadow="never">
          <template #header>
            <span class="role-span">角色列表</span>
          </template>
          <ProTable
            ref="tableRef"
            :requestApi="roleListApi"
            :init-param="filterData"
            @current-change="handleCurrentChange"
            highlight-current-row
            :isShowSearchBtn="false">
            <template #tableHeader>
              <el-button type="primary" @click="createUser">添加角色</el-button>
            </template>
            <el-table-column type="index" label="序号" width="55"></el-table-column>
            <el-table-column prop="name" label="角色名称"></el-table-column>
            <el-table-column prop="description" label="描述"></el-table-column>
            <!-- <el-table-column prop="resources" label="模块权限"></el-table-column> -->
            <el-table-column label="创建时间" :formatter="(row) => $formatDateTime(row.createdTime)"></el-table-column>
            <el-table-column label="操作">
              <template #default="props">
                <el-button type="primary" @click="updateInfo(props.row)">编辑</el-button>
                <el-button type="danger" @click="del(props.row)">删除</el-button>
              </template>
            </el-table-column>
          </ProTable>
        </el-card>
      </el-col>
      <!-- 菜单授权 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="7">
        <el-card class="box-card" shadow="never">
          <template #header>
            <div class="menu-wrap">
              <el-tooltip class="item" effect="dark" content="选择指定角色分配菜单" placement="top">
                <span class="role-span">菜单分配</span>
              </el-tooltip>
              <el-button class="save-menu-btn" :loading="menuLoading" :icon="Check" type="primary" @click="saveMenu">
                保存
              </el-button>
            </div>
          </template>
          <el-tree ref="elTreeRef" :data="menus" :props="defaultProps" default-expand-all show-checkbox node-key="mid" />
        </el-card>
      </el-col>
    </el-row>
    <!-- 新建/编辑 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" center width="400px">
      <el-form ref="dialogFormVue" label-width="100px" :model="dialog.form" :rules="dialog.rules">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="dialog.form.name" clearable></el-input>
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input v-model="dialog.form.description" clearable></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialog.visible = false">取消</el-button>
        <el-button @click="save" type="primary">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="roleList">
import { ref, reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Check } from '@element-plus/icons-vue';

import {
  superSysRoleMenuApi,
  superSysMenusLazyApi,
  roleListApi,
  roleCreateApi,
  roleUpdateApi,
  roleDeleteApi
} from '@/api/modules/system.js';

const filterData = reactive({});
const menuLoading = ref(false);
const menus = ref([]);
const defaultProps = {
  children: 'children',
  label: 'label',
  isLeaf: 'leaf'
};
const dialog = reactive({
  title: '',
  visible: false,
  form: {
    name: '',
    description: '',
    resources: []
  },
  rules: {
    name: [{ required: true, trigger: 'blur', message: '请输入角色名称' }],
    description: [{ required: true, trigger: 'blur', message: '请输入角色描述' }],
    resources: [{ type: 'array', required: true, trigger: 'change', message: '请选择功能权限' }]
  }
});
const elTreeRef = ref();

const { tableRef, refreshDataList } = useTableDom('tableRef');

let currentId = null;

async function getMenuDatas() {
  let res = await superSysMenusLazyApi();
  if (res && !res.hasErrors) {
    menus.value = res.data;
  }
}

function handleCurrentChange(val) {
  console.log('val, elTreeRef.value: ', val, elTreeRef.value);
  if (val) {
    elTreeRef.value.setCheckedKeys([]);
    currentId = val.id;
    val.adIds = val.adIds || [];
    val.adIds.forEach((d) => {
      elTreeRef.value.setChecked(d, true);
    });
  }
}

async function saveMenu() {
  if (!currentId) {
    ElMessage.error('请先选择要修改的角色');
    return;
  }
  const confirmRes = await ElMessageBox.confirm('确定修改吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).catch(() => false);
  if (!confirmRes) return;
  menuLoading.value = true;
  const arr = elTreeRef.value.getCheckedKeys();
  const arr2 = elTreeRef.value.getHalfCheckedKeys();
  let res = await superSysRoleMenuApi({
    id: currentId,
    adIds: arr.concat(arr2)
  });
  if (res && !res.hasErrors) {
    ElMessage.success('操作成功!');
    refreshDataList();
  }
  menuLoading.value = false;
}

async function del(row) {
  const confirmRes = await ElMessageBox.confirm('确定删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).catch(() => false);
  if (!confirmRes) return;
  let res = await roleDeleteApi({ id: row.id });
  if (res) {
    ElMessage.success('删除成功!');
    // 这里可以加刷新逻辑
  }
}

function createUser() {
  dialog.form = {
    name: '',
    description: ''
  };
  dialog.title = '添加角色';
  dialog.visible = true;
  setTimeout(() => {
    dialogFormVue.value && dialogFormVue.value.clearValidate();
  });
}

function updateInfo(row) {
  dialog.form = { ...row };
  dialog.title = '编辑角色';
  dialog.visible = true;
  setTimeout(() => {
    dialogFormVue.value && dialogFormVue.value.clearValidate();
  });
}

async function save() {
  const valid = await dialogFormVue.value.validate().catch(() => false);
  if (!valid) return;
  let res;
  if (dialog.title === '添加角色') {
    res = await roleCreateApi(dialog.form);
  } else {
    res = await roleUpdateApi(dialog.form);
  }
  if (res && !res.hasErrors) {
    ElMessage.success('操作成功');
    dialog.visible = false;
    refreshDataList();
  }
}

const dialogFormVue = ref();
onMounted(() => {
  getMenuDatas();
});
</script>

<style lang="scss" scoped>
.menu-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.save-menu-btn {
  padding: 6px 9px;
}
</style>

<template>
  <div class="tags">
    <template v-if="closable">
      <el-input
        ref="saveTagInput"
        v-model="inputValue"
        class="input-new-tag"
        @keyup.enter="handleInputConfirm"
        @blur="handleInputConfirm" />
      <br />
    </template>
    <el-tag
      size="default"
      v-for="(tag, index) in dynamicTags"
      :key="index"
      :closable="closable"
      :disable-transitions="false"
      @close="handleClose(tag)">
      {{ tag }}
    </el-tag>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { ElInput } from 'element-plus';

defineOptions({ name: 'Tags' });

interface Props {
  tagList: string[] | string;
  closable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  tagList: () => [],
  closable: true
});

const emit = defineEmits<{
  (e: 'update:tagList', val: string[] | string): void;
}>();

const dynamicTags = ref<string[]>([]);
const inputVisible = ref(true);
const inputValue = ref('');
const saveTagInput = ref<InstanceType<typeof ElInput> | null>(null);

watch(
  () => props.tagList,
  (val) => {
    if (typeof val === 'string') {
      dynamicTags.value = val ? val.split(',') : [];
    } else {
      dynamicTags.value = val;
    }
  },
  { immediate: true }
);

const handleClose = (tag: string) => {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1);
  emitUpdate();
};

const handleInputConfirm = () => {
  const value = inputValue.value.trim();
  if (value) {
    dynamicTags.value.push(value);
    emitUpdate();
  }
  inputValue.value = '';
};

const emitUpdate = () => {
  if (typeof props.tagList === 'string') {
    emit('update:tagList', dynamicTags.value.join(','));
  } else {
    emit('update:tagList', [...dynamicTags.value]);
  }
};

const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    saveTagInput.value?.focus?.();
  });
};
</script>

<style lang="scss" scoped>
.tags {
  margin-top: -4px;
}
.el-tag {
  margin-top: 10px;
  margin-right: 10px;
}
.input-new-tag {
  width: 300px;
  margin-right: 10px;
}
</style>

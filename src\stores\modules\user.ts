import { defineStore } from 'pinia';
import type { UserState } from '@/stores/interface';
import piniaPersistConfig from '@/stores/helper/persist';

export const useUserStore = defineStore('xiaohu-platform-admin-user', {
  state: (): UserState => ({
    token: '',
    userInfo: {
      name: ''
    }
  }),

  getters: {
    isLogin: (state) => !!state.token
  },

  actions: {
    setToken(token: string) {
      this.token = token;
    },
    setUserInfo(userInfo: UserState['userInfo']) {
      this.userInfo = userInfo;
    },
    logout() {
      this.token = '';
      this.userInfo = { name: '' };
    }
  },

  // ✅ 如果启用了 pinia-plugin-persist
  persist: piniaPersistConfig('xiaohu-platform-admin-user')
});
